// import nodemailer from "nodemailer";
import similarity from "similarity";

console.log(similarity("hello", "helo"));

// async function main() {
//   let transporter = nodemailer.createTransport({
//     host: "email-smtp.eu-central-1.amazonaws.com123",
//     port: 587,
//     secure: false,
//     logger: true,
//     auth: {
//       user: "AKIAU6TXZYCAVZ7BC2UU123",
//       pass: "BJ31TwC1Rg+ViGFGKw7Bqzj5r9bWrtQz6qCCBNpb20tr123",
//     },
//   });
//   console.log(await transporter);

//   let info = await transporter.sendMail({
//     from: "Development <<EMAIL>>",
//     to: "<EMAIL>",
//     subject: "Hello",
//     text: "Hello world?",
//     html: "<b>Hello world?</b>",
//   });

//   console.log("Message sent: %s", info);
// }

// main();
