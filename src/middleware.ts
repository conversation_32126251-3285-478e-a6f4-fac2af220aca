import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { auth } from "@/server/auth";
import { db } from "@/server/db";

export async function middleware(request: NextRequest) {
  const session = await auth();
  
  // If user is not authenticated, redirect to login
  if (!session?.user) {
    const url = new URL("/login", request.url);
    url.searchParams.set("redirect", request.nextUrl.pathname);
    return NextResponse.redirect(url);
  }
  
  // Check if user is trying to access dashboard
  // if (request.nextUrl.pathname.startsWith("/dashboard")) {
  //   // Check if user has a company
  //   const userCompany = await db.userCompany.findFirst({
  //     where: {
  //       userId: session.user.userId,
  //     },
  //   });
    
  //   // If user doesn't have a company, redirect to boarding
  //   if (!userCompany) {
  //     return NextResponse.redirect(new URL("/boarding", request.url));
  //   }
  // }
  
  return NextResponse.next();
}

export const config = {
  matcher: ["/dashboard/:path*"],
};
