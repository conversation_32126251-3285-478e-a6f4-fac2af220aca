const replacements = [
  {
    charToReplace: "ة",
    replacement: "ه",
  },
  {
    charToReplace: " ال",
    replacement: " ",
  },
  {
    charToReplace: " وال",
    replacement: " ",
  },
];
const isArabic = (str: string) => {
  var arabicPattern = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF]/;
  return arabicPattern.test(str);
};
const getTokenizedText = (text: string) =>
  text
    .toLowerCase()
    .split(" ")
    .filter((a) => Boolean(a));

const getTextLang = (text: string) => (isArabic(text) ? "ar" : "en");

const preprocessText = (text: string) => {
  var txt = text
    ?.replace(/أ/g, "ا")
    ?.replace(/إ/g, "ا")
    ?.replace(/آ/g, "ا")
    .replace(/[,-.,".()\-\s"'؛:|\\?\/؟@#$%^&*~`}{\]\[±§+=\-]/g, " ");
  txt = txt?.toLowerCase();
  for (var i = 0; i < replacements.length; i++) {
    const charToReplace = replacements?.[i]?.charToReplace.toLowerCase();
    const replacement = replacements?.[i]?.replacement;
    txt = ` ${txt} `?.replaceAll(charToReplace || "", replacement || "");
  }
  return txt?.trim();
};
const pipe =
  (...fns: any[]) =>
  (x: any) => {
    const pipeFn = async (v: any, f: any) => {
      return f(await v);
    };
    return fns.reduce(pipeFn, Promise.resolve(x));
  };

export { getTokenizedText, getTextLang, preprocessText, pipe, isArabic };
