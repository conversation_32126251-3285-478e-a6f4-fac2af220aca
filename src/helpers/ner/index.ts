import { getTextLang, getTokenizedText, pipe } from "./helper";
import { type searchPipeObjType } from "./types";
import { preprocessText } from "./helper";
import similarity from "similarity";


const InitSearchResultObject = async (obj: any): Promise<searchPipeObjType> => {
  const enititiesKeys = Object.keys(obj.entitiesData);

  const results: Record<string, any> = {};
  for (var i = 0; i < enititiesKeys.length; i++) {
    results[enititiesKeys[i] || ""] = [];
  }

  return {
    tokenizedText: obj.tokenizedText,
    entities: obj.entitiesData,
    searchMethod: "similarty",
    question: obj.question,
    lang: obj.lang,
    results: {
      ...results,
      toOptimize: null,
    } as any,
    pool: [],
    score: obj.score,
  };
};
const search = async (obj: searchPipeObjType) => {
  var results = [];

  for (var i = 0; i < obj.pool.length; i++) {
    const record = obj.pool[i];
    const searchTerms = preprocessText(record.search_term)?.split(" ") || [];
    const matchedArr = [];
    for (var a = 0; a < obj.tokenizedText.length; a++) {
      for (var s = 0; s < searchTerms.length; s++) {
        const word = obj.tokenizedText[a];
        const searchTerm = searchTerms[s];
        if (obj.searchMethod === "similarty") {
          const sim = similarity(`${searchTerm}`, word || "");
          if (sim > obj.score) {
            matchedArr.push({
              word,
              score: sim,
            });
          }
        }
      }
    }

    if (matchedArr.length) {
      results.push({
        ...record,
        numOfMatchedWords: matchedArr.length,
        matchedArr,
        words: matchedArr.map((a) => a.word),
        questionLength: record?.search_term?.length,
      });
    }
  }
  obj.results.toOptimize = results;
  return obj;
};

const resultOptimizer = (obj: searchPipeObjType) => {
  if (obj.results.toOptimize?.length) {
    var max_word_confident = Math.max.apply(
      Math,
      obj.results.toOptimize.map(function (o: any) {
        return o?.numOfMatchedWords;
      }),
    );
    obj.results.toOptimize = obj.results.toOptimize.filter(
      (a: any) => a.numOfMatchedWords === max_word_confident,
    );

    const lowerCaseQuestion = obj.question.toLowerCase();
    const exactMatches = obj.results.toOptimize.filter((result: any) => {
      return lowerCaseQuestion.includes(result.query_term.toLowerCase());
    });

    if (exactMatches.length > 0) {
      obj.results.toOptimize = exactMatches;
    }

    // Step 3: If we still have multiple results, filter by minimum search term length
    if (obj.results.toOptimize.length > 1) {
      var min_length = Math.min.apply(
        Math,
        obj.results.toOptimize.map(function (o: any) {
          return o?.questionLength;
        }),
      );
      obj.results.toOptimize = obj.results.toOptimize.filter(
        (a: any) => a.questionLength === min_length,
      );
    }

    // Step 4: If we still have multiple results, filter by maximum average score
    if (obj.results.toOptimize.length > 1) {
      obj.results.toOptimize.forEach((result: any) => {
        result.avgScore =
          result.matchedArr.reduce(
            (sum: number, match: any) => sum + match.score,
            0,
          ) / result.matchedArr.length;
      });

      var max_avg_score = Math.max.apply(
        Math,
        obj.results.toOptimize.map(function (o: any) {
          return o?.avgScore;
        }),
      );

      obj.results.toOptimize = obj.results.toOptimize.filter(
        (a: any) => a.avgScore === max_avg_score,
      );
    }
  }
  return obj;
};

const searchInDimensions = async (obj: searchPipeObjType) => {
  const keys = Object.keys(obj.entities);
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i] || "";
    obj.pool = obj.entities[key];
    obj = await pipe(search, resultOptimizer)(obj);
    obj.results[key] = obj.results.toOptimize;
    obj.results.toOptimize = null;
    obj.pool = null;
  }

  return obj.results;
};

const similaritySearch = async (
  question: string,
  entitiesData: Record<
    string,
    {
      search_term: string;
      query_term: string | number;
    }[]
  >,
  score: number = 0.7,
) => {
  const lang = getTextLang(question);
  const tokenizedText = getTokenizedText(preprocessText(question));

  const pipeResult = await pipe(
    InitSearchResultObject,
    searchInDimensions,
  )({
    tokenizedText,
    question,
    lang,
    data_warehouse_id: 1,
    entitiesData,
    score,
  });
  return pipeResult;
};
export { similaritySearch };
