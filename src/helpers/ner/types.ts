// import { NullableType } from "@/packages/utils/types/nullable.type";

type NullableType<T> = T | null;
type searchMethodsTypes = "include" | "exact" | "similarty";

type EntityRecord = {
  search_term: string;
  query_term: string | number;
};

type Entities = Record<string, EntityRecord[]>;

type Results = Record<string, string[] | number[]> & {
  toOptimize: any;
};

export type searchPipeObjType = {
  tokenizedText: string[];
  entities: Entities;
  results: Results;
  lang: string;
  searchMethod: searchMethodsTypes;
  question: string;
  pool: any;
  score: number;
};
