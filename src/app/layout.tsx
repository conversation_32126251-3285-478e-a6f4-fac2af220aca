import "@/styles/globals.css";

import { type <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";

import { TRPCReactProvider } from "@/trpc/react";
import Providers from "@/components/providers";
import { ToastContainer } from "react-toastify";

export const metadata: Metadata = {
  title: "Wilfred<PERSON>",
  description: "Your Best Assistant for Managing Your Business",
  icons: [{ rel: "icon", url: "/wilfredoLogo.png" }],
};

const geist = Geist({
  subsets: ["latin"],
  variable: "--font-geist-sans",
});

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" className={`${geist.variable}`}>
      <body>
        <TRPCReactProvider>
          <Providers>
            <ToastContainer />
            {children}
          </Providers>
        </TRPCReactProvider>
      </body>
    </html>
  );
}
