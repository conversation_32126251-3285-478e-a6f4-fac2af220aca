"use client";

import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Building2,
  MapPin,
  Users,
  Calendar,
  Mail,
  Globe,
  Crown,
  UserCheck,
} from "lucide-react";

interface CompanyData {
  userCompanyId: string;
  role: string;
  userId: string;
  companyId: string;
  createdAt: string;
  updatedAt: string;
  Company: {
    companyId: string;
    realmId: string;
    CompanyName: string;
    LegalName: string;
    CompanyStartDate: string;
    Country: string;
    CompanyAddr: {
      City?: string;
      Line1?: string;
      PostalCode?: string;
      CountrySubDivisionCode?: string;
      Country?: string;
    };
    Email: {
      Address: string;
    };
    WebAddr?: {
      URI?: string;
    };
    PrimaryPhone?: {
      FreeFormNumber?: string;
    };
    NameValue: Array<{
      Name: string;
      Value: string;
    }>;
  };
  UserCompanyState: {
    boardingStep: number;
    done: boolean;
  } | null;
}

interface CompanyCardProps {
  companyData: CompanyData;
  onClick?: () => void;
}

export function CompanyCard({ companyData, onClick }: CompanyCardProps) {
  const { Company, UserCompanyState, role } = companyData;

  // Extract industry from NameValue array
  const industry =
    Company.NameValue.find((nv) => nv.Name === "QBOIndustryType")?.Value ||
    "Business";
  const subscriptionStatus =
    Company.NameValue.find((nv) => nv.Name === "SubscriptionStatus")?.Value ||
    "Unknown";
  const offeringSku =
    Company.NameValue.find((nv) => nv.Name === "OfferingSku")?.Value ||
    "QuickBooks Online";

  // Determine boarding status
  const isBoardingComplete = UserCompanyState?.done ?? false;
  const boardingStep = UserCompanyState?.boardingStep ?? 0;

  // Format address
  const address = Company.CompanyAddr;
  const location =
    [address.City, address.CountrySubDivisionCode, address.Country]
      .filter(Boolean)
      .join(", ") || "Location not specified";

  // Format company start year
  const startYear = new Date(Company.CompanyStartDate).getFullYear();

  // Role badge color
  const getRoleBadgeVariant = (role: string) => {
    switch (role.toLowerCase()) {
      case "owner":
        return "default";
      case "admin":
        return "secondary";
      case "editor":
        return "outline";
      default:
        return "outline";
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role.toLowerCase()) {
      case "owner":
        return <Crown className="h-3 w-3" />;
      case "admin":
      case "editor":
        return <UserCheck className="h-3 w-3" />;
      default:
        return <Users className="h-3 w-3" />;
    }
  };

  return (
    <Card
      className={`w-full max-w-sm cursor-pointer transition-all duration-200 hover:-translate-y-1 hover:shadow-lg ${
        isBoardingComplete
          ? "hover:border-primary/50"
          : "border-orange-200 bg-orange-50/30 hover:border-orange-300"
      }`}
      onClick={onClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex min-w-0 flex-1 items-center space-x-3">
            <Avatar className="h-12 w-12 flex-shrink-0">
              <AvatarImage
                src="/placeholder.svg?height=48&width=48"
                alt={`${Company.CompanyName} logo`}
              />
              <AvatarFallback className="bg-primary/10 text-primary text-sm font-semibold">
                {Company.CompanyName.split(" ")
                  .map((word) => word[0])
                  .join("")
                  .slice(0, 2)
                  .toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="min-w-0 flex-1">
              <CardTitle
                className="truncate text-lg font-semibold"
                title={Company.CompanyName}
              >
                {Company.CompanyName}
              </CardTitle>
              <CardDescription className="text-muted-foreground truncate text-sm">
                {industry}
              </CardDescription>
            </div>
          </div>
          <div className="flex flex-shrink-0 flex-col items-end gap-1">
            <Badge
              variant={getRoleBadgeVariant(role)}
              className="flex items-center gap-1 text-xs"
            >
              {getRoleIcon(role)}
              {role}
            </Badge>
            {!isBoardingComplete && (
              <Badge
                variant="outline"
                className="border-orange-300 bg-orange-100 text-xs text-orange-700"
              >
                Setup Required
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {Company.LegalName !== Company.CompanyName && (
          <div className="text-sm">
            <span className="text-muted-foreground">Legal Name: </span>
            <span className="font-medium">{Company.LegalName}</span>
          </div>
        )}

        <div className="grid grid-cols-1 gap-2 text-sm">
          <div className="text-muted-foreground flex items-center space-x-2">
            <MapPin className="h-4 w-4 flex-shrink-0" />
            <span className="truncate" title={location}>
              {location}
            </span>
          </div>

          {Company.Email?.Address && (
            <div className="text-muted-foreground flex items-center space-x-2">
              <Mail className="h-4 w-4 flex-shrink-0" />
              <span className="truncate" title={Company.Email.Address}>
                {Company.Email.Address}
              </span>
            </div>
          )}

          {Company.WebAddr?.URI && (
            <div className="text-muted-foreground flex items-center space-x-2">
              <Globe className="h-4 w-4 flex-shrink-0" />
              <span className="truncate" title={Company.WebAddr.URI}>
                {Company.WebAddr.URI}
              </span>
            </div>
          )}
        </div>

        <div className="text-muted-foreground grid grid-cols-2 gap-3 border-t pt-2 text-xs">
          <div className="flex items-center space-x-1">
            <Calendar className="h-3 w-3" />
            <span>Est. {startYear}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Building2 className="h-3 w-3" />
            <span className="truncate">{Company.Country}</span>
          </div>
        </div>

        <div className="text-xs">
          <Badge variant="secondary" className="text-xs">
            {offeringSku}
          </Badge>
        </div>

        {!isBoardingComplete && UserCompanyState && (
          <div className="border-t border-orange-200 pt-2">
            <div className="flex items-center justify-between">
              <p className="text-xs font-medium text-orange-600">
                Setup Step {boardingStep} - Complete setup to access all
                features
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
