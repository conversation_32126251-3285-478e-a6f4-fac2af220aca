import "@/styles/globals.css";
// import { type Metadata } from "next";

import { auth } from "@/server/auth";
import ProfileDropdown from "../../../_components/profileDropdown";

import Link from "next/link";
import { Box, Camera, FileScan, House, ScanText } from "lucide-react";

// export const metadata: Metadata = {
//   title: "Wilfredo",
//   description: "Generated by wilfredo",
//   icons: [{ rel: "icon", url: "/favicon.ico" }],
// };

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ companyId: string }>;
}>) {
  const { companyId } = await params;
  // const params = useParams() as { companyId: string };
  // const router = useRouter();
  const session = await auth();
  // if (!session) {
  //   return redirect("/login");
  // }
  return (
    <div className="flex h-dvh flex-col overflow-hidden">
      <div className="fixed top-0 right-0 left-0 z-10 flex h-16 items-center justify-center">
        <div className="flex h-full w-full justify-between p-3">
          <p className="font-bold">WILFREDO</p>
          <ProfileDropdown user={session?.user as any} companyId={companyId} />
        </div>
      </div>
      {/* if no bottom remove the mb-12 */}
      <div className="mt-16 flex-1 overflow-y-auto pb-20">{children}</div>

      <div className="fixed bottom-2 left-1/2 z-50 h-16 w-full max-w-lg -translate-x-1/2 rounded-full border border-gray-200 bg-white">
        <div className="mx-auto grid h-full max-w-lg grid-cols-5">
          <Link
            className="group inline-flex flex-col items-center justify-center rounded-s-full px-5 hover:bg-gray-50 dark:hover:bg-gray-800"
            href={""}
          >
            <Box className="mb-1 h-5 w-5 text-gray-500 group-hover:text-blue-600 dark:text-gray-400 dark:group-hover:text-blue-500" />
          </Link>

          <Link
            href={`/company/${companyId}/ocr`}
            className="group inline-flex flex-col items-center justify-center px-5 hover:bg-gray-50 dark:hover:bg-gray-800"
          >
            <Camera 
              // fill="currentColor"
              className="mb-1 h-5 w-5 text-gray-500 group-hover:text-blue-600 dark:text-gray-400 dark:group-hover:text-blue-500"
            />
            <span className="sr-only">Wallet</span>
          </Link>

          <div className="flex items-center justify-center">
            <Link
              href={`/company/${companyId}`}
              className="group inline-flex h-10 w-10 items-center justify-center rounded-full bg-blue-600 font-medium duration-150 hover:-translate-y-1 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 focus:outline-none dark:focus:ring-blue-800"
            >
              <House className="h-4 w-4 text-white" />
            </Link>
          </div>

          <Link
            href={""}
            className="group inline-flex flex-col items-center justify-center px-5 hover:bg-gray-50 dark:hover:bg-gray-800"
          >
            <Box className="mb-1 h-5 w-5 text-gray-500 group-hover:text-blue-600 dark:text-gray-400 dark:group-hover:text-blue-500" />
          </Link>

          <Link
            href={``}
            className="group inline-flex flex-col items-center justify-center rounded-e-full px-5 hover:bg-gray-50 dark:hover:bg-gray-800"
          >
            <Box className="mb-1 h-5 w-5 text-gray-500 group-hover:text-blue-600 dark:text-gray-400 dark:group-hover:text-blue-500" />
          </Link>
        </div>
      </div>
    </div>
  );
}
