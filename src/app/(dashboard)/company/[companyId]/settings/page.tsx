"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import {
  Building2,
  <PERSON>tings,
  UserPlus,
  User,
  ChevronRight,
} from "lucide-react";

import { api } from "@/trpc/react";
import { toast } from "react-toastify";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Plus, MoreHorizontal } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const settingsItems = [
  {
    id: "profile",
    label: "Profile",
    icon: User,
    description: "Manage your personal information",
    badge: null,
    active: false,
  },
  {
    id: "company",
    label: "Companies",
    icon: Building2,
    description: "Update company details",
    badge: null,
    active: false,
  },
  {
    id: "invite",
    label: "Team members",
    icon: UserPlus,
    description: "Invite & manage team members",
    // badge: "New",
    active: true,
  },
  {
    id: "preferences",
    label: "Preferences",
    icon: Settings,
    description: "Configure your preferences",
    badge: null,
    active: false,
  },
];

export default function Component() {
  const params = useParams() as { companyId: string };

  // Find the first active item and set it as default
  const firstActiveItem = settingsItems.find((item) => item.active);
  const [activeSection, setActiveSection] = useState(
    firstActiveItem?.id || "invite",
  );
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);

  // Form state for invite modal
  const [inviteEmail, setInviteEmail] = useState("");
  const [inviteRole, setInviteRole] = useState<"admin" | "editor" | "member">(
    "member",
  );

  // State for editing roles
  const [editingUser, setEditingUser] = useState<{
    email: string;
    currentRole: string;
  } | null>(null);
  const [newRole, setNewRole] = useState<"admin" | "editor" | "member">(
    "member",
  );

  // Fetch people data from API
  const {
    data: people,
    isLoading: isPeopleLoading,
    error: peopleError,
    refetch: refetchPeople,
  } = api.settings.people.useQuery({
    companyId: params.companyId,
  });

  // Invite person mutation
  const invitePersonMutation = api.settings.invitePerson.useMutation({
    onSuccess: (data) => {
      // Reset form
      setInviteEmail("");
      setInviteRole("member");
      setIsInviteModalOpen(false);

      // Refetch people data
      refetchPeople();

      // Show success message
      toast.success(data.message);
    },
    onError: (error) => {
      // Show error message
      toast.error(error.message);
    },
  });

  // Update person role mutation
  const updatePersonRoleMutation = api.settings.updatePersonRole.useMutation({
    onSuccess: (data) => {
      // Refetch people data
      refetchPeople();

      // Reset editing state
      setEditingUser(null);
      setNewRole("member");

      // Show success message
      toast.success(data.message);
    },
    onError: (error) => {
      // Show error message
      toast.error(error.message);
    },
  });

  // Delete person mutation
  const deletePersonMutation = api.settings.deletePerson.useMutation({
    onSuccess: (data) => {
      // Refetch people data
      refetchPeople();

      // Show success message
      toast.success(data.message);
    },
    onError: (error) => {
      // Show error message
      toast.error(error.message);
    },
  });

  const handleInviteSubmit = () => {
    if (!inviteEmail.trim()) {
      toast.error("Please enter an email address");
      return;
    }

    invitePersonMutation.mutate({
      companyId: params.companyId,
      email: inviteEmail.trim(),
      role: inviteRole,
    });
  };

  const handleUpdateRole = (email: string, currentRole: string) => {
    setEditingUser({ email, currentRole });
    setNewRole(currentRole as "admin" | "editor" | "member");
  };

  const handleSaveRole = () => {
    if (!editingUser) return;

    updatePersonRoleMutation.mutate({
      companyId: params.companyId,
      email: editingUser.email,
      role: newRole,
    });
  };

  const handleDeletePerson = (email: string) => {
    if (confirm(`Are you sure you want to remove ${email} from the company?`)) {
      deletePersonMutation.mutate({
        companyId: params.companyId,
        email,
      });
    }
  };

  const activeItem = settingsItems.find((item) => item.id === activeSection);

  const renderContent = () => {
    switch (activeSection) {
      case "profile":
        return (
          <div className="space-y-8">
            <div className="space-y-2">
              <h3 className="text-2xl font-semibold tracking-tight">
                Profile Settings
              </h3>
              <p className="text-muted-foreground">
                Update your personal information and profile details.
              </p>
            </div>
            <Separator className="my-6" />
            <div className="grid gap-6">
              <div className="grid gap-3">
                <Label htmlFor="fullName" className="text-sm font-medium">
                  Full Name
                </Label>
                <Input
                  id="fullName"
                  placeholder="Enter your full name"
                  className="h-11"
                />
              </div>
              <div className="grid gap-3">
                <Label htmlFor="email" className="text-sm font-medium">
                  Email Address
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  className="h-11"
                />
              </div>
            </div>
          </div>
        );
      case "company":
        return (
          <div className="space-y-8">
            <div className="space-y-2">
              <h3 className="text-2xl font-semibold tracking-tight">
                Company Settings
              </h3>
              <p className="text-muted-foreground">
                Manage your company information and settings.
              </p>
            </div>
            <Separator className="my-6" />
            <div className="grid gap-6">
              <div className="grid gap-3">
                <Label htmlFor="companyName" className="text-sm font-medium">
                  Company Name
                </Label>
                <Input
                  id="companyName"
                  placeholder="Enter company name"
                  className="h-11"
                />
              </div>
              <div className="grid gap-3">
                <Label htmlFor="website" className="text-sm font-medium">
                  Website
                </Label>
                <Input
                  id="website"
                  placeholder="https://yourcompany.com"
                  className="h-11"
                />
              </div>
            </div>
          </div>
        );
      case "invite":
        return (
          <div className="space-y-8">
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <h3 className="text-2xl font-semibold tracking-tight">
                  Team members
                </h3>
                <Badge variant="secondary" className="text-xs">
                  New
                </Badge>
              </div>
              <p className="text-muted-foreground">
                Manage & Invite team members to join your organization.
                <br />
                <small>
                  Assign roles to team members to manage access to different
                  features.
                </small>
              </p>
            </div>
            <Separator className="my-6" />

            {/* Add Button */}
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium md:text-lg">Team Members</h4>
                <p className="text-muted-foreground text-sm md:text-sm">
                  {isPeopleLoading
                    ? "Loading team members..."
                    : people
                      ? `${people.length} team member${people.length !== 1 ? "s" : ""}`
                      : "Manage your team invitations"}
                </p>
              </div>
              <Dialog
                open={isInviteModalOpen}
                onOpenChange={setIsInviteModalOpen}
              >
                <DialogTrigger asChild>
                  <Button size="sm" className="gap-2">
                    <Plus className="h-4 w-4" />
                    Add Member
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>Invite Team Member</DialogTitle>
                  </DialogHeader>
                  <div className="grid gap-6 py-4">
                    <div className="grid gap-3">
                      <Label
                        htmlFor="modalEmail"
                        className="text-sm font-medium"
                      >
                        Email Address
                      </Label>
                      <Input
                        id="modalEmail"
                        type="email"
                        placeholder="<EMAIL>"
                        className="h-11"
                        value={inviteEmail}
                        onChange={(e) => setInviteEmail(e.target.value)}
                      />
                    </div>
                    <div className="grid gap-3">
                      <Label
                        htmlFor="modalRole"
                        className="text-sm font-medium"
                      >
                        Role
                      </Label>
                      <Select
                        value={inviteRole}
                        onValueChange={(value: "admin" | "editor" | "member") =>
                          setInviteRole(value)
                        }
                      >
                        <SelectTrigger className="h-11">
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="member">Member</SelectItem>
                          <SelectItem value="editor">Editor</SelectItem>
                          <SelectItem value="admin">Admin</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex gap-3 pt-4">
                      <Button
                        variant="outline"
                        className="flex-1 bg-transparent"
                        onClick={() => {
                          setIsInviteModalOpen(false);
                          setInviteEmail("");
                          setInviteRole("member");
                        }}
                        disabled={invitePersonMutation.isPending}
                      >
                        Cancel
                      </Button>
                      <Button
                        className="flex-1"
                        onClick={handleInviteSubmit}
                        disabled={invitePersonMutation.isPending}
                      >
                        {invitePersonMutation.isPending
                          ? "Sending..."
                          : "Send Invite"}
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            {/* Table */}
            <div className="rounded-lg border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Email</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Invited Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="w-[50px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isPeopleLoading ? (
                    <TableRow>
                      <TableCell colSpan={5} className="py-8 text-center">
                        Loading people...
                      </TableCell>
                    </TableRow>
                  ) : peopleError ? (
                    <TableRow>
                      <TableCell
                        colSpan={5}
                        className="py-8 text-center text-red-600"
                      >
                        Error loading people: {peopleError.message}
                      </TableCell>
                    </TableRow>
                  ) : !people || people.length === 0 ? (
                    <TableRow>
                      <TableCell
                        colSpan={5}
                        className="text-muted-foreground py-8 text-center"
                      >
                        No people found. Start by inviting team members.
                      </TableCell>
                    </TableRow>
                  ) : (
                    people.map((person, index) => (
                      <TableRow key={`${person.email}-${index}`}>
                        <TableCell className="font-medium">
                          {person.email}
                        </TableCell>
                        <TableCell>
                          {editingUser?.email === person.email ? (
                            <div className="flex items-center gap-2">
                              <Select
                                value={newRole}
                                onValueChange={(
                                  value: "admin" | "editor" | "member",
                                ) => setNewRole(value)}
                              >
                                <SelectTrigger className="h-8 w-24">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="member">Member</SelectItem>
                                  <SelectItem value="editor">Editor</SelectItem>
                                  <SelectItem value="admin">Admin</SelectItem>
                                </SelectContent>
                              </Select>
                              <Button
                                size="sm"
                                onClick={handleSaveRole}
                                disabled={updatePersonRoleMutation.isPending}
                                className="h-8 px-2"
                              >
                                Save
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  setEditingUser(null);
                                  setNewRole("member");
                                }}
                                className="h-8 px-2"
                              >
                                Cancel
                              </Button>
                            </div>
                          ) : (
                            <Badge
                              variant="outline"
                              className="text-xs capitalize"
                            >
                              {person.role}
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-muted-foreground">
                          {person.invitedAt
                            ? new Date(person.invitedAt).toLocaleDateString()
                            : person.joinedAt
                              ? new Date(person.joinedAt).toLocaleDateString()
                              : "N/A"}
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              person.status === "accepted"
                                ? "default"
                                : "secondary"
                            }
                            className="text-xs capitalize"
                          >
                            {person.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                                disabled={editingUser?.email === person.email}
                              >
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              {person.role !== "owner" && (
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleUpdateRole(person.email, person.role)
                                  }
                                >
                                  Edit Role
                                </DropdownMenuItem>
                              )}
                              {person.status === "pending" && (
                                <DropdownMenuItem>
                                  Resend Invite
                                </DropdownMenuItem>
                              )}
                              {person.role !== "owner" && (
                                <DropdownMenuItem
                                  className="text-red-600"
                                  onClick={() =>
                                    handleDeletePerson(person.email)
                                  }
                                  disabled={deletePersonMutation.isPending}
                                >
                                  Remove
                                </DropdownMenuItem>
                              )}
                              {person.role === "owner" && (
                                <DropdownMenuItem disabled>
                                  Owner cannot be modified
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        );
      case "preferences":
        return (
          <div className="space-y-8">
            <div className="space-y-2">
              <h3 className="text-2xl font-semibold tracking-tight">
                Preferences
              </h3>
              <p className="text-muted-foreground">
                Configure your application preferences and settings.
              </p>
            </div>
            <Separator className="my-6" />
            <div className="grid gap-6">
              <div className="grid gap-3">
                <Label htmlFor="timezone" className="text-sm font-medium">
                  Timezone
                </Label>
                <Input
                  id="timezone"
                  placeholder="Select your timezone"
                  className="h-11"
                />
              </div>
              <div className="grid gap-3">
                <Label htmlFor="language" className="text-sm font-medium">
                  Language
                </Label>
                <Input
                  id="language"
                  placeholder="Select language"
                  className="h-11"
                />
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100/50">
      <div className="container mx-auto px-4 py-8 lg:px-8">
        <div className="mb-8 space-y-2">
          <h1 className="text-4xl font-bold tracking-tight">Settings</h1>
          <p className="text-muted-foreground text-lg">
            Manage your account settings and preferences.
          </p>
        </div>

        <Card className="mx-auto w-full max-w-7xl border-0 bg-white/80 shadow-xl backdrop-blur-sm">
          <div className="flex min-h-[600px] flex-col lg:flex-row">
            {/* Mobile Header */}
            <div className="border-b bg-slate-50/50 p-4 lg:hidden">
              <Button
                variant="ghost"
                className="h-12 w-full justify-between"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                <span className="flex items-center gap-3">
                  {activeItem && <activeItem.icon className="h-5 w-5" />}
                  {activeItem?.label}
                </span>
                <ChevronRight
                  className={`h-4 w-4 transition-transform ${isMobileMenuOpen ? "rotate-90" : ""}`}
                />
              </Button>
            </div>

            {/* Left Sidebar */}
            <div
              className={`w-full border-b bg-slate-50/50 lg:w-80 lg:border-r lg:border-b-0 ${isMobileMenuOpen ? "block" : "hidden lg:block"}`}
            >
              <CardHeader className="px-6 pt-8 pb-6">
                <CardTitle className="text-xl font-semibold">
                  Navigation
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <nav className="space-y-2 p-6 pt-0">
                  {settingsItems
                    .filter((item) => item.active) // Only show active items
                    .map((item) => {
                      const Icon = item.icon;
                      const isActive = activeSection === item.id;
                      return (
                        <Button
                          key={item.id}
                          variant="ghost"
                          className={`h-auto w-full justify-start p-4 transition-all duration-200 hover:bg-white hover:shadow-sm ${
                            isActive
                              ? "border border-slate-200 bg-white text-slate-900 shadow-sm"
                              : "hover:translate-x-1"
                          }`}
                          onClick={() => {
                            // Only allow navigation to active items
                            if (item.active) {
                              setActiveSection(item.id);
                              setIsMobileMenuOpen(false);
                            }
                          }}
                        >
                          <Icon
                            className={`mr-4 h-5 w-5 ${isActive ? "text-slate-700" : "text-slate-500"}`}
                          />
                          <div className="flex-1 text-left">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium">
                                {item.label}
                              </span>
                              {/* {item.badge && (
                                <Badge
                                  variant="secondary"
                                  className="ml-2 text-xs"
                                >
                                  {item?.badge || ""}
                                </Badge>
                              )} */}
                            </div>
                            <div className="text-muted-foreground mt-1 hidden text-xs sm:block">
                              {item.description}
                            </div>
                          </div>
                        </Button>
                      );
                    })}
                </nav>
              </CardContent>
            </div>

            {/* Right Content */}
            <div className="flex flex-1 flex-col">
              <CardContent className="flex-1 p-8 lg:p-12">
                <div className="max-w-2xl">{renderContent()}</div>
              </CardContent>

              {/* Footer Actions */}
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
