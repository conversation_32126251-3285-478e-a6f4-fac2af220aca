"use client";

import { use<PERSON>emo, useState, useEffect } from "react";
import { CalendarIcon, Loader, Plus, Trash2 } from "lucide-react";
import { format, addDays, addMonths, setDate, setDay } from "date-fns";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import type { OcrResponseI } from "@/types/ocrResponse";
import { SearchableSelectDialog } from "@/components/ui/searchable-select-modal";
import { Dialog } from "@/components/ui/dialog";
import { api } from "@/trpc/react";
import { useParams } from "next/navigation";

export default function BillValidationForm({
  ocrResponse,
  onSubmit,
  loading,
}: {
  ocrResponse: OcrResponseI;
  onSubmit: (bill: any) => Promise<any>;
  loading: boolean;
}) {
  const params = useParams() as { companyId: string };
  const [askIfCash, setAskIfCash] = useState(true);
  const [isCashDialogOpen, setIsCashDialogOpen] = useState(false);
  const [isCash, setIsCash] = useState(false);

  const [billData, setBillData] = useState({ ...ocrResponse.bill }); // TODO: add doc number
  const [txnDate, setTxnDate] = useState<Date>(
    new Date(ocrResponse?.bill?.TxnDate || ""),
  );
  const [dueDate, setDueDate] = useState<Date>(
    new Date(ocrResponse?.bill?.DueDate || ""),
  );
  const { data: accounts } = api.accounts.getBankAccounts.useQuery({
    companyId: params.companyId,
  });
  useEffect(() => {
    console.log(accounts);
  }, [accounts]);
  useEffect(() => {
    const date1 = `${txnDate.getFullYear()}-${txnDate.getMonth()}-${txnDate.getDate()}`;
    const date2 = `${dueDate.getFullYear()}-${dueDate.getMonth()}-${dueDate.getDate()}`;

    if (date1 == date2) {
      if (askIfCash) {
        setAskIfCash(false);
        setIsCashDialogOpen(true);
      }
    } else {
      // remove the new keys from the bill data
      // setBillData({
      //   ...billData,
      //   PaymentMethodRef: undefined,
      // });
      setIsCash(false);
      setAskIfCash(true);
    }
  }, [dueDate, txnDate]);
  // Function to calculate due date based on selected term
  const calculateDueDate = (termId: string, transactionDate: Date) => {
    const selectedTerm = ocrResponse?.terms?.find((term) => term.Id === termId);

    if (!selectedTerm) return transactionDate;

    let calculatedDueDate = new Date(transactionDate);

    // Apply the term rules for due date calculation
    if (selectedTerm.DueNextMonthDays) {
      const months = Math.floor(selectedTerm.DueNextMonthDays / 30);
      calculatedDueDate = addMonths(calculatedDueDate, months);
      calculatedDueDate = setDate(
        calculatedDueDate,
        selectedTerm.DayOfMonthDue,
      );
    } else if (selectedTerm.DayOfMonthDue) {
      calculatedDueDate = setDate(
        calculatedDueDate,
        selectedTerm.DayOfMonthDue,
      );
    } else if (selectedTerm.DueDays) {
      calculatedDueDate = addDays(calculatedDueDate, selectedTerm.DueDays);
    }
    return calculatedDueDate;
  };

  // Recalculate due date when transaction date changes and a term is selected
  useEffect(() => {
    if (billData?.SalesTermRef?.value && txnDate) {
      const newDueDate = calculateDueDate(billData.SalesTermRef.value, txnDate);
      setDueDate(newDueDate);
    }
  }, [txnDate, billData?.SalesTermRef?.value, ocrResponse?.terms]);

  type LineLevelField = "Description" | "Amount";
  type ItemDetailField = "ItemRef" | "UnitPrice" | "Qty" | "TaxCodeRef";

  type Field = LineLevelField | ItemDetailField;

  const updateLineItem = (
    index: number,
    field: Field,
    value: any, // we'll type this better below
  ) => {
    const newLines = [...(billData?.Line || [])];
    if (!newLines?.[index]) return;

    if (field === "Description" || field === "Amount") {
      (newLines[index] as any)[field] = value;
    } else {
      (newLines[index].ItemBasedExpenseLineDetail as any)[field] = value;
    }

    if (field === "Qty" || field === "UnitPrice") {
      const qty =
        field === "Qty"
          ? value
          : newLines?.[index]?.ItemBasedExpenseLineDetail?.Qty;
      const unitPrice =
        field === "UnitPrice"
          ? value
          : newLines?.[index]?.ItemBasedExpenseLineDetail?.UnitPrice;
      newLines[index].Amount = qty * unitPrice;
    }

    setBillData({ ...billData, Line: newLines });
  };
  const addLineItem = () => {
    const newLine = {
      DetailType: "ItemBasedExpenseLineDetail",
      Description: "",
      Amount: 0,
      ItemBasedExpenseLineDetail: {
        ItemRef: { value: "" },
        UnitPrice: 0,
        Qty: 1,
        TaxCodeRef: { value: "5" },
      },
    };
    setBillData({ ...billData, Line: [...(billData?.Line || []), newLine] });
  };

  const removeLineItem = (index: number) => {
    const newLines = billData?.Line?.filter((_, i) => i !== index);
    setBillData({ ...billData, Line: newLines });
  };

  const getTotalAmount = () => {
    return billData?.Line?.reduce((sum, line) => sum + (line?.Amount || 0), 0);
  };

  const handleSubmit = () => {
    if (loading || errors) return;

    const finalBillData = {
      ...billData,
      Line: billData?.Line?.map((line) => ({
        ...line,
        ItemBasedExpenseLineDetail: {
          ...line.ItemBasedExpenseLineDetail,
          ItemRef: { value: line.ItemBasedExpenseLineDetail?.ItemRef?.value },
        },
      })),
      VendorRef: { value: billData?.VendorRef?.value },
      TxnDate: format(txnDate, "yyyy-MM-dd"),
      DueDate: format(dueDate, "yyyy-MM-dd"),
    };
    console.log(finalBillData);
    if (isCash) {
      const payload = {
        GlobalTaxCalculation: "TaxExcluded",
        Line: finalBillData.Line,
        DocNumber: finalBillData.DocNumber,
        TxnDate: finalBillData.TxnDate,
        PaymentType: "Cash",
        PaymentMethodRef: finalBillData.PaymentMethodRef,
        PrivateNote: finalBillData.PrivateNote,
        AccountRef: { value: finalBillData?.AccountRef?.value },
        EntityRef: {
          type: "Vendor",
          value: finalBillData.VendorRef.value,
        },
      };
      console.log(payload);
      onSubmit(payload);
    } else {
      onSubmit(finalBillData);
    }
  };
  const errors = useMemo(() => {
    console.log(billData);

    return (
      billData?.Line?.some(
        (line) => !line?.ItemBasedExpenseLineDetail?.ItemRef?.value,
      ) || !billData?.VendorRef?.value
    );
  }, [billData]);
  return (
    <div className="mx-auto max-w-4xl space-y-6">
      <AlertDialog open={isCashDialogOpen} onOpenChange={setIsCashDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Was this paid cash?</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to create this bill as a cash transaction?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                setBillData({
                  ...billData,
                  PaymentMethodRef: { value: "1" },
                  AccountRef: {
                    value:
                      accounts?.find((acc) => acc.Name == "Cash on hand")?.Id || // need to be defined in the settings
                      undefined,
                  },
                });
                setIsCash(true);
                setAskIfCash(false);
              }}
            >
              Yes, create as cash transaction
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <Card>
        <CardHeader>
          <CardTitle>Validate Bill Information</CardTitle>
          <CardDescription>
            Review and edit the extracted bill details before creating in
            QuickBooks
          </CardDescription>
        </CardHeader>
        <CardContent className="relative space-y-6">
          {/* Vendor Selection and Bill Number */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="vendor">
                Vendor{" "}
                {!billData?.VendorRef?.value ? (
                  <small className="text-xs text-red-500">
                    please select a vendor
                  </small>
                ) : null}
              </Label>
              <SearchableSelectDialog
                options={
                  ocrResponse?.vendors?.map((vendor) => ({
                    value: vendor.Id,
                    label: vendor.DisplayName,
                  })) || []
                }
                value={billData?.VendorRef?.value}
                onValueChange={(value: any) =>
                  setBillData({ ...billData, VendorRef: { value } })
                }
                placeholder="Select vendor"
                searchPlaceholder="Search vendors..."
                className="w-full"
              />
            </div>
          </div>

          {/* Payment Terms */}
          {!isCash ? (
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="terms">Payment Terms</Label>
                <SearchableSelectDialog
                  options={
                    ocrResponse?.terms?.map((term) => ({
                      value: term.Id,
                      label: term.Name,
                    })) || []
                  }
                  value={billData?.SalesTermRef?.value}
                  onValueChange={(value: any) => {
                    // Calculate and set the due date based on the selected term
                    if (txnDate) {
                      const newDueDate = calculateDueDate(value, txnDate);
                      setDueDate(newDueDate);
                    }

                    setBillData({ ...billData, SalesTermRef: { value } });
                  }}
                  placeholder="Select terms"
                  searchPlaceholder="Search terms..."
                  className="w-full"
                />
              </div>
            </div>
          ) : null}
          {isCash ? (
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="vendor">
                  Payment Account
                  {!billData?.VendorRef?.value ? (
                    <small className="text-xs text-red-500">
                      please select an account
                    </small>
                  ) : null}
                </Label>
                <SearchableSelectDialog
                  options={
                    accounts?.map((acc) => ({
                      value: acc.Id || "",
                      label: acc.Name || "",
                    })) || []
                  }
                  value={billData?.AccountRef?.value}
                  onValueChange={(value: any) =>
                    setBillData({ ...billData, AccountRef: { value } })
                  }
                  placeholder="Select account"
                  searchPlaceholder="Search accounts..."
                  className="w-full"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="vendor">
                  Payment Method
                  {!billData?.VendorRef?.value ? (
                    <small className="text-xs text-red-500">
                      please select payment method
                    </small>
                  ) : null}
                </Label>
                <SearchableSelectDialog
                  options={[
                    {
                      value: "1",
                      label: "Cash",
                    },
                    {
                      value: "2",
                      label: "Cheque",
                    },
                    {
                      value: "3",
                      label: "Credit Card",
                    },
                    {
                      value: "4",
                      label: "Debit Card",
                    },
                    {
                      value: "5",
                      label: "Direct Debit",
                    },
                    {
                      value: "6",
                      label: "Online Transfer",
                    },
                    {
                      value: "7",
                      label: "Post Dated Cheque",
                    },
                  ]}
                  value={billData?.PaymentMethodRef?.value}
                  onValueChange={(value: any) =>
                    setBillData({ ...billData, PaymentMethodRef: { value } })
                  }
                  placeholder="Select payment method"
                  searchPlaceholder="Search payment methods..."
                  className="w-full"
                />
              </div>
            </div>
          ) : null}

          {/* Dates */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label>{isCash ? "Payment Date" : "Transaction Date"}</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !txnDate && "text-muted-foreground",
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {txnDate ? (
                      format(txnDate, "PPP")
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={txnDate}
                    onSelect={(date: any) => date && setTxnDate(date)}
                    // initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            {!isCash ? (
              <div className="space-y-2">
                <Label>Due Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !dueDate && "text-muted-foreground",
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dueDate ? (
                        format(dueDate || txnDate, "PPP")
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dueDate}
                      onSelect={(date: any) => date && setDueDate(date)}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            ) : null}
          </div>

          {/* Line Items */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-lg font-semibold">Line Items</Label>
              <Button onClick={addLineItem} size="sm" variant="outline">
                <Plus className="mr-2 h-4 w-4" />
                Add Item
              </Button>
            </div>

            {billData?.Line?.map((line, index) => (
              <Card
                key={index}
                className={`${line?.ItemBasedExpenseLineDetail?.ItemRef?.value ? "" : "border-red-500"} p-4`}
              >
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="font-medium">Item {index + 1}</Label>
                    {(billData?.Line?.length || 0) > 1 && (
                      <Button
                        onClick={() => removeLineItem(index)}
                        size="sm"
                        variant="ghost"
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="space-y-3">
                    <div>
                      <Label htmlFor={`item-${index}`}>
                        Item
                        {line?.ItemBasedExpenseLineDetail?.ItemRef
                          ?.value ? null : (
                          <small className="text-xs text-red-500">
                            please select an item
                          </small>
                        )}
                      </Label>
                      <SearchableSelectDialog
                        options={
                          ocrResponse?.items?.map((item) => ({
                            value: item.Id,
                            label: item.Name,
                          })) || []
                        }
                        value={line?.ItemBasedExpenseLineDetail?.ItemRef?.value}
                        onValueChange={(value: any) =>
                          updateLineItem(index, "ItemRef", { value })
                        }
                        placeholder="Select item"
                        searchPlaceholder="Search items..."
                        className="w-full"
                      />
                    </div>

                    <div>
                      <Label htmlFor={`description-${index}`}>
                        Description
                      </Label>
                      <Textarea
                        id={`description-${index}`}
                        value={line.Description}
                        onChange={(e) =>
                          updateLineItem(index, "Description", e.target.value)
                        }
                        className="min-h-[60px]"
                      />
                    </div>

                    <div className="grid grid-cols-1 gap-3 md:grid-cols-3">
                      <div>
                        <Label htmlFor={`qty-${index}`}>Quantity</Label>
                        <Input
                          id={`qty-${index}`}
                          type="number"
                          value={line?.ItemBasedExpenseLineDetail?.Qty}
                          onChange={(e) =>
                            updateLineItem(
                              index,
                              "Qty",
                              Number.parseFloat(e.target.value) || 0,
                            )
                          }
                        />
                      </div>

                      <div>
                        <Label htmlFor={`price-${index}`}>Unit Price</Label>
                        <Input
                          id={`price-${index}`}
                          type="number"
                          step="0.01"
                          value={line?.ItemBasedExpenseLineDetail?.UnitPrice}
                          onChange={(e) =>
                            updateLineItem(
                              index,
                              "UnitPrice",
                              Number.parseFloat(e.target.value) || 0,
                            )
                          }
                        />
                      </div>

                      <div>
                        <Label htmlFor={`tax-${index}`}>Tax</Label>
                        <SearchableSelectDialog
                          options={
                            ocrResponse?.taxes?.map((tax) => ({
                              value: tax.id,
                              label: `${tax.name} (${tax.rate}%)`,
                            })) || []
                          }
                          value={
                            line?.ItemBasedExpenseLineDetail?.TaxCodeRef?.value
                          }
                          onValueChange={(value: any) =>
                            updateLineItem(index, "TaxCodeRef", { value })
                          }
                          placeholder="Select tax"
                          searchPlaceholder="Search tax codes..."
                          className="w-full"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Private Notes</Label>
            <Textarea
              id="notes"
              value={billData?.PrivateNote}
              onChange={(e) =>
                setBillData({ ...billData, PrivateNote: e.target.value })
              }
              className="min-h-[80px]"
            />
          </div>

          {/* Total */}

          {/* Actions */}
          <div className="flex flex-col gap-3 pt-4 sm:flex-row-reverse">
            <AlertDialog>
              <AlertDialogTrigger asChild disabled={loading || errors}>
                <Button className="flex-1" disabled={loading || errors}>
                  {loading ? (
                    <Loader className="animate-spin" />
                  ) : (
                    "Create Bill in QuickBooks"
                  )}
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    Please confirm you want to create this bill.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleSubmit}>
                    Create Bill
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
            <AlertDialog>
              <AlertDialogTrigger asChild disabled={loading}>
                <Button
                  className="flex-1"
                  variant="destructive"
                  disabled={loading}
                >
                  Discard
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    Please confirm you want to discard this bill. You will lose
                    your progress.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={() => {
                      window.location.reload();
                    }}
                  >
                    Discard
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
