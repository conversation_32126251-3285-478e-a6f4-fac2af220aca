"use client";
import type React from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FileText, Trash2, Upload, Lightbulb, Loader } from "lucide-react";
import { GoogleGenAI, Type } from "@google/genai";
import { api } from "@/trpc/react";
import { useParams } from "next/navigation";
import BillValidationForm from "./components/bill-validation-form";
import { toast } from "react-toastify";
import heic2any from "heic2any";

export default function BillUpload() {
  // const params = useParams() as { companyId: string };
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const params = useParams() as { companyId: string };
  const [base64, setBase64] = useState<string | null>(null);
  const [isCompressing, setIsCompressing] = useState(false);

  const {
    mutateAsync: OCRBill,
    data: ocrBillData,
    isPending: OCRLoading,
  } = api.ocr.ocr.useMutation();
  const { mutateAsync: createBill, isPending: createBillLoading } =
    api.ocr.createBill.useMutation();

  // Function to compress image
  const compressImage = async (
    file: File,
    maxWidth = 1920,
    quality = 0.8,
  ): Promise<string> => {
    return new Promise(async (resolve, reject) => {
      let imageBlob: Blob;

      try {
        if (file.type === "image/heic" || file.name.endsWith(".heic")) {
          console.warn("Converting HEIC image to JPEG...");
          const convertedBlob = await heic2any({
            blob: file,
            toType: "image/jpeg",
            quality,
          });
          imageBlob = convertedBlob as Blob;
        } else {
          imageBlob = file;
        }

        const img = document.createElement("img");
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");

        img.onload = () => {
          let { width, height } = img;
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }

          canvas.width = width;
          canvas.height = height;
          ctx?.drawImage(img, 0, 0, width, height);
          const dataUrl = canvas.toDataURL("image/jpeg", quality);
          resolve(dataUrl);
        };

        img.onerror = (err) => reject(err);
        img.src = URL.createObjectURL(imageBlob);
      } catch (error) {
        console.error("Failed to convert HEIC:", error);
        reject(error);
      }
    });
  };

  const handleFileInput = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (!selectedFile) return;

    // Check file size (50MB limit to be safe)
    const maxSizeInBytes = 50 * 1024 * 1024; // 50MB
    if (selectedFile.size > maxSizeInBytes) {
      toast.error(
        "File is too large. Please select an image smaller than 50MB.",
      );
      return;
    }

    if (selectedFile.type.startsWith("image/")) {
      // Show processing message for large files
      if (selectedFile.size > 5 * 1024 * 1024) {
        // 5MB
        toast.info("Processing large image, please wait...");
      }

      // preview part
      const compressedBase64 = await compressImage(selectedFile);
      setFile(selectedFile);
      setBase64(compressedBase64);
      setPreview(compressedBase64); // <== update preview from base64 JPEG

      // Compress image before converting to base64
      try {
        setIsCompressing(true);
        const compressedBase64 = await compressImage(selectedFile);
        setBase64(compressedBase64);
        const sizeKB = Math.round(compressedBase64.length / 1024);
        console.log("Compressed Base64 size:", sizeKB, "KB");

        if (sizeKB > 10000) {
          // 10MB in base64
          toast.warning(
            "Image is still quite large after compression. Processing may take longer.",
          );
        }
      } catch (error) {
        console.error("Error compressing image:", error);
        toast.error("Failed to process image. Please try a different image.");
        setBase64(null);
        setFile(null);
        setPreview(null);
      } finally {
        setIsCompressing(false);
      }
    } else {
      toast.error("Please select an image file (JPG, PNG, or PDF)");
      setBase64(null); // Not an image
    }
  };

  const handleDelete = () => {
    setFile(null);
    setPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleOCR = async () => {
    if (!OCRLoading && base64) {
      toast.info("Click on me to copy the data", {
        onClick: () => {
          // copy to clipboard
          navigator.clipboard.writeText(
            JSON.stringify({
              companyId: params.companyId,
              base64: base64,
            }),
          );
          toast.success("now send it to azzam on whatsapp");
        },
      });
      try {
        toast.info("Processing bill...");
        const res = await OCRBill({
          companyId: params.companyId,
          base64: base64,
        });
        if (!res?.bill) {
          return handleOCR();
        }
      } catch (error: any) {
        // alert(JSON.stringify(error));
        console.error("OCR Error:", error);
        if (
          error?.message?.includes("413") ||
          error?.message?.includes("too large")
        ) {
          toast.error(
            "Image is too large. Please try a smaller image or take a new photo.",
          );
        } else if (error?.message?.includes("timeout")) {
          toast.error(
            "Processing timed out. Please try again with a smaller image.",
          );
        } else {
          // alert(JSON.stringify(error.message));
          toast.error(
            "Failed to process bill. Please provide a clearer image.",
          );
        }
      }
    }
  };

  const handleCreateBill = async (bill: any) => {
    if (createBillLoading || !base64) return;

    try {
      toast.info("Creating Bill...");
      const res = await createBill({
        companyId: params.companyId,
        base64: base64,
        bill,
      });

      if (res.success) {
        toast.success("Created Bill successfully", {
          onClick: () => {
            window.open("/company/123", "_blank");
          },
        });
        // wait then refresh the page
        setTimeout(() => {
          window.location.reload();
        }, 2500);
      } else {
        toast.error("Failed to create bill. Please try again.");
      }
    } catch (error: any) {
      console.error("Create Bill Error:", error);
      if (
        error?.message?.includes("413") ||
        error?.message?.includes("too large")
      ) {
        toast.error(
          "Image is too large. Please try a smaller image or take a new photo.",
        );
      } else if (error?.message?.includes("timeout")) {
        toast.error(
          "Processing timed out. Please try again with a smaller image.",
        );
      } else {
        toast.error("Failed to create bill. Please try again.");
      }
    }
  };

  return (
    <>
      {ocrBillData ? (
        <BillValidationForm
          ocrResponse={ocrBillData as any}
          onSubmit={handleCreateBill}
          loading={createBillLoading}
        />
      ) : (
        <div className="p-3">
          <Card className="mx-auto max-w-md p-6">
            <div className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-lg font-medium">
                  Upload Bill to QuickBooks
                </h3>
                <p className="text-muted-foreground text-sm">
                  Upload a bill or take a picture to quickly add it to your
                  QuickBooks account.
                </p>
              </div>

              <div className="space-y-4">
                <div className="space-y-3">
                  <Label
                    htmlFor="picture"
                    className="block text-base font-medium"
                  >
                    Upload Your Bill
                  </Label>
                  <div className="relative">
                    <Input
                      id="picture"
                      type="file"
                      ref={fileInputRef}
                      onChange={handleFileInput}
                      accept="image/png, image/jpeg, image/jpg, image/webp, image/heic, image/heif, application/pdf"
                      className="sr-only absolute"
                    />

                    <div
                      onClick={() => fileInputRef.current?.click()}
                      className="flex cursor-pointer items-center justify-center gap-2 rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 px-4 py-3 transition-colors hover:bg-gray-100"
                    >
                      <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full">
                        <Upload className="text-primary h-5 w-5" />
                      </div>
                      <div className="text-left">
                        <p className="text-sm font-medium">Click to upload</p>
                        <p className="text-muted-foreground hidden text-xs md:block">
                          or drag and drop
                        </p>
                        <p className="text-muted-foreground block text-xs md:hidden">
                          or take a picture
                        </p>
                      </div>
                    </div>
                    {file && (
                      <p className="text-muted-foreground mt-2 text-sm">
                        Selected:{" "}
                        <span className="text-foreground font-medium">
                          {file.name}
                        </span>
                      </p>
                    )}
                  </div>
                </div>

                <div className="text-muted-foreground space-y-2 text-sm">
                  <p className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Accepted formats: JPG, PNG, PDF
                  </p>
                  <p className="flex items-center gap-2">
                    <Upload className="h-4 w-4" />
                    Images are automatically compressed for faster processing
                  </p>
                </div>
                <Accordion type="single" collapsible>
                  <AccordionItem value="item-1">
                    <AccordionTrigger>
                      <div className="flex items-start gap-2">
                        <Lightbulb className="mt-0.5 h-5 w-5 flex-shrink-0 text-amber-500" />
                        <div className="space-y-1">
                          <p className="text-sm font-medium text-amber-800">
                            Tips for successful bill processing
                          </p>
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <ul className="list-disc space-y-1 pl-4 text-xs text-amber-700">
                        <li>
                          Ensure the image is clear, well-lit, and not blurry
                        </li>
                        <li>Capture the entire bill with all edges visible</li>
                        <li>
                          Make sure the date, amount, and vendor name are
                          clearly visible
                        </li>
                        <li>Flatten crumpled bills before taking a photo</li>
                        <li>Avoid shadows or glare on the document</li>
                        <li>
                          For best results, place the bill on a dark,
                          contrasting background
                        </li>
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>

                {preview && (
                  <div className="space-y-3">
                    <div className="text-sm font-medium">Preview</div>
                    <div className="relative h-32 w-32 overflow-hidden rounded-md border">
                      {file?.type.includes("image") ? (
                        <Image
                          src={preview || "/placeholder.svg"}
                          alt="Bill preview"
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="bg-muted flex h-full items-center justify-center">
                          <FileText className="text-muted-foreground h-10 w-10" />
                        </div>
                      )}
                      <Button
                        variant="destructive"
                        size="icon"
                        className="absolute top-1 right-1 h-6 w-6"
                        onClick={handleDelete}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                    <div className="max-w-[200px] truncate text-sm">
                      {file?.name}
                    </div>
                  </div>
                )}

                {base64 && (
                  <Button
                    disabled={OCRLoading || isCompressing}
                    onClick={handleOCR}
                    className="w-full"
                  >
                    {OCRLoading ? (
                      <>
                        <Loader className="mr-2 h-4 w-4 animate-spin" />
                        Processing Bill...
                      </>
                    ) : isCompressing ? (
                      <>
                        <Loader className="mr-2 h-4 w-4 animate-spin" />
                        Compressing Image...
                      </>
                    ) : (
                      "Process Bill"
                    )}
                  </Button>
                )}
              </div>
            </div>
          </Card>
        </div>
      )}
    </>
  );
}
