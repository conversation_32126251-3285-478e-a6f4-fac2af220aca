"use client";

import { useState, useMemo } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Building2,
  Search,
  Users,
  AlertTriangle,
  Calendar,
  DollarSign,
  Clock,
  FileText,
  Eye,
} from "lucide-react";
import Link from "next/link";

// Mock vendors data (simplified)
const mockVendors = [
  {
    vendorId: "vendor_001",
    DisplayName: "ABC Supply Company",
    CompanyName: "ABC Supply Company LLC",
    Active: true,
    Balance: 2450.75,
    PrimaryEmailAddr: { Address: "<EMAIL>" },
    PrimaryPhone: { FreeFormNumber: "(*************" },
  },
  {
    vendorId: "vendor_002",
    DisplayName: "Tech Solutions Inc",
    CompanyName: "Tech Solutions Incorporated",
    Active: true,
    Balance: -1200.5,
    PrimaryEmailAddr: { Address: "<EMAIL>" },
    PrimaryPhone: { FreeFormNumber: "(*************" },
  },
  {
    vendorId: "vendor_003",
    DisplayName: "Green Energy Corp",
    CompanyName: "Green Energy Corporation",
    Active: true,
    Balance: 5750.25,
    PrimaryEmailAddr: { Address: "<EMAIL>" },
    PrimaryPhone: { FreeFormNumber: "(*************" },
  },
  {
    vendorId: "vendor_004",
    DisplayName: "Office Supplies Plus",
    CompanyName: "Office Supplies Plus LLC",
    Active: false,
    Balance: 0.0,
    PrimaryEmailAddr: { Address: "<EMAIL>" },
    PrimaryPhone: { FreeFormNumber: "(*************" },
  },
  {
    vendorId: "vendor_005",
    DisplayName: "Construction Materials Co",
    CompanyName: "Construction Materials Company",
    Active: true,
    Balance: 3200.8,
    PrimaryEmailAddr: { Address: "<EMAIL>" },
    PrimaryPhone: { FreeFormNumber: "(*************" },
  },
  {
    vendorId: "vendor_006",
    DisplayName: "Digital Marketing Pro",
    CompanyName: "Digital Marketing Professionals",
    Active: true,
    Balance: -850.0,
    PrimaryEmailAddr: { Address: "<EMAIL>" },
    PrimaryPhone: { FreeFormNumber: "(*************" },
  },
];

// Mock upcoming bills data
const mockUpcomingBills = [
  {
    billId: "bill_001",
    vendorId: "vendor_001",
    vendorName: "ABC Supply Company",
    billNumber: "BILL-2024-001",
    description: "Office supplies and equipment",
    amount: 1250.0,
    dueDate: "2025-01-05", // 2 days from now
    status: "Open",
    priority: "high",
  },
  {
    billId: "bill_002",
    vendorId: "vendor_002",
    vendorName: "Tech Solutions Inc",
    billNumber: "BILL-2024-002",
    description: "Monthly software licenses",
    amount: 850.5,
    dueDate: "2025-01-04", // 1 day from now
    status: "Open",
    priority: "high",
  },
  {
    billId: "bill_003",
    vendorId: "vendor_003",
    vendorName: "Green Energy Corp",
    billNumber: "BILL-2024-003",
    description: "Quarterly energy consultation",
    amount: 2200.0,
    dueDate: "2025-01-06", // 3 days from now
    status: "Open",
    priority: "high",
  },
  {
    billId: "bill_004",
    vendorId: "vendor_005",
    vendorName: "Construction Materials Co",
    billNumber: "BILL-2024-004",
    description: "Building materials delivery",
    amount: 3500.75,
    dueDate: "2025-01-08", // 5 days from now
    status: "Open",
    priority: "medium",
  },
  {
    billId: "bill_005",
    vendorId: "vendor_006",
    vendorName: "Digital Marketing Pro",
    billNumber: "BILL-2024-005",
    description: "December marketing services",
    amount: 1800.0,
    dueDate: "2025-01-10", // 7 days from now
    status: "Open",
    priority: "medium",
  },
  {
    billId: "bill_006",
    vendorId: "vendor_001",
    vendorName: "ABC Supply Company",
    billNumber: "BILL-2024-006",
    description: "Additional office equipment",
    amount: 675.25,
    dueDate: "2025-01-12", // 9 days from now
    status: "Open",
    priority: "low",
  },
];

export default function VendorsDashboard() {
  const [vendorSearchTerm, setVendorSearchTerm] = useState("");

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getDaysUntilDue = (dueDate: string) => {
    const today = new Date("2025-01-03"); // Mock current date
    const due = new Date(dueDate);
    const diffTime = due.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-50 text-red-700 border-red-200";
      case "medium":
        return "bg-yellow-50 text-yellow-700 border-yellow-200";
      case "low":
        return "bg-green-50 text-green-700 border-green-200";
      default:
        return "bg-gray-50 text-gray-700 border-gray-200";
    }
  };

  // Filter vendors by search term
  const filteredVendors = useMemo(() => {
    return mockVendors.filter((vendor) =>
      vendor.DisplayName.toLowerCase().includes(vendorSearchTerm.toLowerCase()),
    );
  }, [vendorSearchTerm]);

  // Calculate stats
  const stats = useMemo(() => {
    const today = new Date("2025-01-03"); // Mock current date
    const threeDaysFromNow = new Date(today);
    threeDaysFromNow.setDate(today.getDate() + 3);

    const billsDueNext3Days = mockUpcomingBills.filter((bill) => {
      const dueDate = new Date(bill.dueDate);
      return dueDate <= threeDaysFromNow && dueDate >= today;
    });

    const totalVendors = mockVendors.filter((v) => v.Active).length;

    return {
      billsDueNext3Days: billsDueNext3Days.length,
      totalVendors,
      billsDueAmount: billsDueNext3Days.reduce(
        (sum, bill) => sum + bill.amount,
        0,
      ),
    };
  }, []);

  // Sort upcoming bills by due date
  const sortedUpcomingBills = useMemo(() => {
    return [...mockUpcomingBills].sort(
      (a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime(),
    );
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      {/* Header */}
      <div className="border-b border-slate-200 bg-white shadow-sm dark:border-slate-700 dark:bg-slate-800">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center space-x-4">
              <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-blue-600">
                <Building2 className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                  Vendors
                </h1>
                <p className="text-sm text-slate-500 dark:text-slate-400">
                  Manage vendors and track upcoming bills
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="mx-auto max-w-7xl space-y-8 px-4 py-8 sm:px-6 lg:px-8">
        {/* Basic Stats */}
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <Card className="border-red-200 bg-gradient-to-br from-red-50 to-red-100 dark:border-red-800 dark:from-red-900/20 dark:to-red-800/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-red-600 dark:text-red-400">
                    Bills Due Next 3 Days
                  </p>
                  <p className="mt-2 text-3xl font-bold text-red-900 dark:text-red-100">
                    {stats.billsDueNext3Days}
                  </p>
                  <p className="mt-1 text-sm text-red-700 dark:text-red-300">
                    Total: {formatCurrency(stats.billsDueAmount)}
                  </p>
                </div>
                <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-red-500">
                  <AlertTriangle className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100 dark:border-blue-800 dark:from-blue-900/20 dark:to-blue-800/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600 dark:text-blue-400">
                    Active Vendors
                  </p>
                  <p className="mt-2 text-3xl font-bold text-blue-900 dark:text-blue-100">
                    {stats.totalVendors}
                  </p>
                  <p className="mt-1 text-sm text-blue-700 dark:text-blue-300">
                    Total vendors in system
                  </p>
                </div>
                <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-blue-500">
                  <Users className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Vendors Table */}
        <Card className="border-slate-200 shadow-sm dark:border-slate-700">
          <CardHeader>
            <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
              <CardTitle className="flex items-center space-x-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/30">
                  <Building2 className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <span>Vendors</span>
              </CardTitle>
              <div className="relative max-w-sm">
                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-slate-400" />
                <Input
                  placeholder="Search vendors..."
                  value={vendorSearchTerm}
                  onChange={(e) => setVendorSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-slate-50 dark:bg-slate-800/50">
                    <TableHead className="font-semibold">Vendor Name</TableHead>
                    <TableHead className="font-semibold">Contact</TableHead>
                    <TableHead className="text-right font-semibold">
                      Balance
                    </TableHead>
                    <TableHead className="font-semibold">Status</TableHead>
                    <TableHead className="font-semibold">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredVendors.slice(0, 8).map((vendor, index) => (
                    <TableRow
                      key={vendor.vendorId}
                      className={
                        index % 2 === 0
                          ? "bg-white dark:bg-slate-900"
                          : "bg-slate-50/50 dark:bg-slate-800/25"
                      }
                    >
                      <TableCell>
                        <div>
                          <p className="font-medium text-slate-900 dark:text-white">
                            {vendor.DisplayName}
                          </p>
                          <p className="max-w-xs truncate text-sm text-slate-500 dark:text-slate-400">
                            {vendor.CompanyName}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <p className="max-w-xs truncate text-sm text-slate-900 dark:text-white">
                            {vendor.PrimaryEmailAddr?.Address}
                          </p>
                          <p className="text-sm text-slate-500 dark:text-slate-400">
                            {vendor.PrimaryPhone?.FreeFormNumber}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <span
                          className={`font-bold ${vendor.Balance >= 0 ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"}`}
                        >
                          {formatCurrency(vendor.Balance)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={vendor.Active ? "default" : "secondary"}
                          className={
                            vendor.Active
                              ? "bg-green-100 text-green-800 hover:bg-green-200"
                              : ""
                          }
                        >
                          {vendor.Active ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/vendor/${vendor.vendorId}`}>
                            <Eye className="mr-2 h-4 w-4" />
                            View
                          </Link>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Bills */}
        <Card className="border-slate-200 shadow-sm dark:border-slate-700">
          <CardHeader>
            <CardTitle className="flex items-center space-x-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-orange-100 dark:bg-orange-900/30">
                <FileText className="h-4 w-4 text-orange-600 dark:text-orange-400" />
              </div>
              <span>Upcoming Bills</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-slate-50 dark:bg-slate-800/50">
                    <TableHead className="font-semibold">Bill #</TableHead>
                    <TableHead className="font-semibold">Vendor</TableHead>
                    <TableHead className="font-semibold">Description</TableHead>
                    <TableHead className="text-right font-semibold">
                      Amount
                    </TableHead>
                    <TableHead className="font-semibold">Due Date</TableHead>
                    <TableHead className="font-semibold">
                      Days Until Due
                    </TableHead>
                    <TableHead className="font-semibold">Priority</TableHead>
                    <TableHead className="font-semibold">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedUpcomingBills.map((bill, index) => {
                    const daysUntilDue = getDaysUntilDue(bill.dueDate);
                    return (
                      <TableRow
                        key={bill.billId}
                        className={
                          index % 2 === 0
                            ? "bg-white dark:bg-slate-900"
                            : "bg-slate-50/50 dark:bg-slate-800/25"
                        }
                      >
                        <TableCell>
                          <span className="rounded bg-slate-100 px-2 py-1 font-mono text-sm dark:bg-slate-800">
                            {bill.billNumber}
                          </span>
                        </TableCell>
                        <TableCell>
                          <p className="font-medium text-slate-900 dark:text-white">
                            {bill.vendorName}
                          </p>
                        </TableCell>
                        <TableCell>
                          <p className="max-w-xs truncate text-sm text-slate-600 dark:text-slate-400">
                            {bill.description}
                          </p>
                        </TableCell>
                        <TableCell className="text-right">
                          <span className="font-bold text-slate-900 dark:text-white">
                            {formatCurrency(bill.amount)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-slate-400" />
                            <span className="text-sm text-slate-600 dark:text-slate-400">
                              {formatDate(bill.dueDate)}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Clock className="h-4 w-4 text-slate-400" />
                            <span
                              className={`text-sm font-medium ${
                                daysUntilDue <= 1
                                  ? "text-red-600 dark:text-red-400"
                                  : daysUntilDue <= 3
                                    ? "text-yellow-600 dark:text-yellow-400"
                                    : "text-slate-600 dark:text-slate-400"
                              }`}
                            >
                              {daysUntilDue === 0
                                ? "Due Today"
                                : daysUntilDue === 1
                                  ? "1 day"
                                  : `${daysUntilDue} days`}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={`font-medium ${getPriorityColor(bill.priority)}`}
                          >
                            {bill.priority.charAt(0).toUpperCase() +
                              bill.priority.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button variant="ghost" size="sm">
                              <Eye className="mr-1 h-4 w-4" />
                              View
                            </Button>
                            <Button variant="ghost" size="sm">
                              <DollarSign className="mr-1 h-4 w-4" />
                              Pay
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
