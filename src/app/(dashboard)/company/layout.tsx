import "@/styles/globals.css";
// import { type Metadata } from "next";

import { auth } from "@/server/auth";
// import ProfileDropdown from "../../../_components/profileDropdown";

import Link from "next/link";
import { FileScan, House, ScanText } from "lucide-react";
import ProfileDropdown from "@/app/_components/profileDropdown";

// export const metadata: Metadata = {
//   title: "Wilfredo",
//   description: "Generated by wilfredo",
//   icons: [{ rel: "icon", url: "/favicon.ico" }],
// };

export default async function RootLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ companyId: string }>;
}>) {
  const { companyId } = await params;
  // const params = useParams() as { companyId: string };
  // const router = useRouter();
  const session = await auth();
  // if (!session) {
  //   return redirect("/login");
  // }
  return (
    <div className="flex h-dvh flex-col overflow-hidden">
      <div className="fixed top-0 right-0 left-0 z-10 flex h-16 items-center justify-center">
        <div className="flex h-full w-full justify-between p-3">
          <p className="font-bold">WILFREDO</p>
          <ProfileDropdown user={session?.user as any} companyId={companyId} />
        </div>
      </div>
      {/* if no bottom remove the mb-12 */}
      {children}
    </div>
  );
}
