import { api } from "@/trpc/server";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Building2,
  MapPin,
  Users,
  Calendar,
  Mail,
  Globe,
  Crown,
  UserCheck,
} from "lucide-react";
import Link from "next/link";
import { redirect } from "next/navigation";

// Type definitions for the company data structure
interface CompanyData {
  userCompanyId: string;
  role: string;
  userId: string;
  companyId: string;
  createdAt: Date;
  updatedAt: Date;
  Company: {
    companyId: string;
    realmId: string;
    CompanyName: string | null;
    LegalName: string | null;
    CompanyStartDate: string | null;
    Country: string | null;
    CompanyAddr: {
      City?: string;
      Line1?: string;
      PostalCode?: string;
      CountrySubDivisionCode?: string;
      Country?: string;
    } | null;
    Email: {
      Address: string;
    } | null;
    WebAddr: {
      URI?: string;
    } | null;
    PrimaryPhone?: {
      FreeFormNumber?: string;
    } | null;
    NameValue: Array<{
      Name: string;
      Value: string;
    }> | null;
  };
  UserCompanyState: {
    boardingStep: number;
    done: boolean;
  } | null;
}

const Page = async () => {
  const userInfo = await api.user.getUserInformation();
  if (userInfo?.length == 1 && userInfo) {
    redirect(`/company/${userInfo?.[0]?.companyId}`);
  }
  return (
    <div className="min-h-screen bg-white p-4 sm:p-6">
      <div className="mx-auto max-w-7xl">
        {/* Header Section */}
        <div className="mb-8 text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center">
            <Building2 className="h-8 w-8 text-gray-600" />
          </div>
          <h1 className="mb-2 text-2xl font-semibold text-gray-900">
            Choose a Company
          </h1>
          <p className="text-gray-600">
            Select a company to continue and access your dashboard
          </p>
        </div>

        {/* Companies Grid */}
        <div className="grid gap-4 sm:grid-cols-2">
          {userInfo?.map((companyData) => {
            const { Company, UserCompanyState, role } =
              companyData as CompanyData;

            // Type-safe access to NameValue array
            const nameValueArray = Array.isArray(Company.NameValue)
              ? (Company.NameValue as Array<{ Name: string; Value: string }>)
              : [];

            // Extract industry from NameValue array
            const industry =
              nameValueArray.find((nv) => nv.Name === "QBOIndustryType")
                ?.Value || "Business";

            const offeringSku =
              nameValueArray.find((nv) => nv.Name === "OfferingSku")?.Value ||
              "QuickBooks Online";

            // Determine boarding status
            const isBoardingComplete = UserCompanyState?.done ?? false;
            const boardingStep = UserCompanyState?.boardingStep ?? 0;

            // Format address - handle null case
            const address = Company.CompanyAddr as {
              City?: string;
              CountrySubDivisionCode?: string;
              Country?: string;
            } | null;

            const location = address
              ? [address.City, address.CountrySubDivisionCode, address.Country]
                  .filter(Boolean)
                  .join(", ") || "Location not specified"
              : "Location not specified";

            // Format company start year - handle null case
            const startYear = Company.CompanyStartDate
              ? new Date(Company.CompanyStartDate).getFullYear()
              : new Date().getFullYear();

            // Role badge color
            const getRoleBadgeVariant = (role: string) => {
              switch (role.toLowerCase()) {
                case "owner":
                  return "default";
                case "admin":
                  return "secondary";
                case "editor":
                  return "outline";
                default:
                  return "outline";
              }
            };

            const getRoleIcon = (role: string) => {
              switch (role.toLowerCase()) {
                case "owner":
                  return <Crown className="h-3 w-3" />;
                case "admin":
                case "editor":
                  return <UserCheck className="h-3 w-3" />;
                default:
                  return <Users className="h-3 w-3" />;
              }
            };

            return (
              <Link
                key={Company.companyId}
                href={`/company/${Company.companyId}`}
              >
                <Card className="h-full cursor-pointer border border-gray-200 bg-white transition-colors hover:border-gray-300">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between gap-3">
                      <div className="flex min-w-0 flex-1 items-center gap-3">
                        <Avatar className="h-10 w-10 flex-shrink-0">
                          {/* <AvatarImage
                            src="/placeholder.svg?height=40&width=40"
                            alt={`${Company.CompanyName || "Company"} logo`}
                          /> */}
                          <AvatarFallback className="bg-gray-100 text-sm font-medium text-gray-700">
                            {(Company.CompanyName || "Company")
                              .split(" ")
                              .map((word) => word[0])
                              .join("")
                              .slice(0, 2)
                              .toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="min-w-0 flex-1">
                          <CardTitle className="text-base leading-tight font-medium text-gray-900">
                            {Company.CompanyName || "Unnamed Company"}
                          </CardTitle>
                          <CardDescription className="mt-1 text-sm text-gray-500">
                            {industry}
                          </CardDescription>
                        </div>
                      </div>
                      <div className="flex flex-col gap-1">
                        <Badge
                          variant={getRoleBadgeVariant(role)}
                          className="flex items-center gap-1 text-xs whitespace-nowrap"
                        >
                          {getRoleIcon(role)}
                          {role}
                        </Badge>
                        {!isBoardingComplete && (
                          <Badge
                            variant="outline"
                            className="text-xs whitespace-nowrap"
                          >
                            Setup Required
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-3 pt-0">
                    {Company.LegalName &&
                      Company.LegalName !== Company.CompanyName && (
                        <div className="text-sm">
                          <span className="text-gray-500">Legal: </span>
                          <span className="text-gray-900">
                            {Company.LegalName}
                          </span>
                        </div>
                      )}

                    <div className="space-y-2">
                      <div className="flex items-start gap-2 text-sm text-gray-600">
                        <MapPin className="mt-0.5 h-4 w-4 flex-shrink-0" />
                        <span className="break-words">{location}</span>
                      </div>

                      {(() => {
                        const email = Company.Email as {
                          Address: string;
                        } | null;
                        return (
                          email?.Address && (
                            <div className="flex items-start gap-2 text-sm text-gray-600">
                              <Mail className="mt-0.5 h-4 w-4 flex-shrink-0" />
                              <span className="break-all">{email.Address}</span>
                            </div>
                          )
                        );
                      })()}

                      {(() => {
                        const webAddr = Company.WebAddr as {
                          URI?: string;
                        } | null;
                        return (
                          webAddr?.URI && (
                            <div className="flex items-start gap-2 text-sm text-gray-600">
                              <Globe className="mt-0.5 h-4 w-4 flex-shrink-0" />
                              <span className="break-all">{webAddr.URI}</span>
                            </div>
                          )
                        );
                      })()}
                    </div>

                    <div className="flex items-center justify-between border-t pt-3 text-xs text-gray-500">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>Est. {startYear}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Building2 className="h-3 w-3" />
                        <span>{Company.Country || "Unknown"}</span>
                      </div>
                    </div>

                    <div>
                      <Badge variant="secondary" className="text-xs">
                        {offeringSku}
                      </Badge>
                    </div>

                    {!isBoardingComplete && UserCompanyState && (
                      <div className="border-t pt-3">
                        <p className="text-xs text-gray-600">
                          Setup Step {boardingStep} - Complete setup to access
                          all features
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </Link>
            );
          })}
        </div>

        {/* Empty State */}
        {!userInfo ||
          (userInfo.length === 0 && (
            <div className="py-12 text-center">
              <Building2 className="mx-auto mb-4 h-12 w-12 text-gray-400" />
              <h3 className="mb-2 text-lg font-medium text-gray-900">
                No companies found
              </h3>
              <p className="text-gray-600">
                You don't have access to any companies yet.
              </p>
            </div>
          ))}
      </div>
    </div>
  );
};

export default Page;
