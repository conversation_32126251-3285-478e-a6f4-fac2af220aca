import "@/styles/globals.css";

import { type Metadata } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";

import { auth } from "@/server/auth";
import ProfileDropdown from "../_components/profileDropdown";
import { redirect } from "next/navigation";

// export const metadata: Metadata = {
//   title: "Create T3 App",
//   description: "Generated by create-t3-app",
//   icons: [{ rel: "icon", url: "/favicon.ico" }],
// };

const geist = Geist({
  subsets: ["latin"],
  variable: "--font-geist-sans",
});

export default async function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const session = await auth();
  if (!session) {
    return redirect("/login");
  }
  return (
    <div className="h-full">
      <div className="flex h-12 w-full justify-between p-3">
        <p className="font-bold ">WILFREDO</p>
        <ProfileDropdown user={session?.user as any} />
      </div>
      {children}
    </div>
  );
}
