"use client";
import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";

import Stepper, { Step } from "@/components/ui/Stepper";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { api } from "@/trpc/react";
import type { ETLJobLogStatus } from "@prisma/client";
import {
  Loader,
  Database,
  CheckCircle2,
  AlertCircle,
  Clock,
  Zap,
  Building2,
  ArrowRight,
  RefreshCw,
} from "lucide-react";

const BoardingPage = () => {
  const router = useRouter();
  const params = useParams() as { companyId: string };
  const session = useSession();

  const [loading, setLoading] = useState(false);
  const [etlJobRunning, setEtlJobRunning] = useState(false);

  const { data: company, refetch: refetchCompany } =
    api.company.getCompany.useQuery({
      companyId: params.companyId,
    });

  const { data } = api.sse.on.useSubscription(
    {
      eventName: "etl",
      channelId: params.companyId,
      lastEventId: params.companyId,
    },
    {
      onData: async (data) => {
        console.log("data", data);
        setEtlJobRunning(true);
        if (data?.done) {
          await refetchCompany();
          setEtlJobRunning(false);
        }
      },
    },
  );

  const { mutateAsync: startEtlProcess } =
    api.quickbooks.startEtlProcess.useMutation();
  const { mutateAsync: updateBoardingState } =
    api.quickbooks.updateBoardingState.useMutation({
      onSuccess: () => {},
    });

  const handleStartEtl = async (refetch = false) => {
    setEtlJobRunning(true);
    await startEtlProcess({
      companyId: params.companyId,
      trigger: "BOARDING",
      refetch: refetch,
    });
    setEtlJobRunning(false);
  };

  if (session.status === "loading") {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <div className="mb-6">
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-indigo-600">
                <Database className="h-8 w-8 animate-pulse text-white" />
              </div>
            </div>
            <h2 className="mb-2 text-2xl font-bold text-gray-900">
              Setting things up...
            </h2>
            <p className="mb-6 text-gray-600">
              Please wait while we prepare your workspace
            </p>
            <Progress className="h-2 w-full bg-gray-200" value={33} />
            <div className="mt-3 text-sm text-gray-500">
              Loading your session
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const companyName = session.data?.user?.UserCompanies.find(
    (c) => c?.companyId === params.companyId,
  )?.Company?.CompanyName;

  return (
    <Stepper
      initialStep={2}
      disableStepIndicators
      stepsNames={[
        "Connect QuickBooks",
        "Extract Data",
        "Account Review",
        "Finish Up",
      ]}
      nextButton={(step) => {
        const boardingJob = company?.etlJobs?.find(
          (job) => job.trigger === "BOARDING",
        );

        if (!boardingJob) {
          return (
            <Button
              onClick={() => handleStartEtl()}
              disabled={etlJobRunning}
              className="w-full transform bg-gradient-to-r from-blue-600 to-indigo-600 shadow-lg transition-all duration-200 hover:scale-105 hover:from-blue-700 hover:to-indigo-700 md:w-fit"
              size="lg"
            >
              <Zap className="mr-2 h-4 w-4" />
              Start Data Extraction
            </Button>
          );
        }

        if (etlJobRunning) {
          return (
            <Button disabled className="w-full md:w-fit" size="lg">
              <Loader className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </Button>
          );
        }

        if (!etlJobRunning && boardingJob?.status === "RUNNING") {
          return (
            <div className="w-full items-center gap-3 md:flex md:w-fit">
              <div className="mb-2 md:mb-0">
                <Badge variant="destructive" className="text-xs">
                  <AlertCircle className="mr-1 h-3 w-3" />
                  Process interrupted
                </Badge>
              </div>
              <Button
                onClick={() => handleStartEtl(true)}
                disabled={etlJobRunning}
                className="w-full bg-orange-500 hover:bg-orange-600 md:w-fit"
                size="lg"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Resume Process
              </Button>
            </div>
          );
        }

        return (
          <>
            {boardingJob?.status === "FAILED" ? (
              <div className="flex w-full flex-col gap-3 sm:flex-row md:w-fit">
                <Button
                  variant="outline"
                  onClick={() => handleStartEtl(true)}
                  disabled={etlJobRunning}
                  className="border-orange-200 text-orange-700 hover:bg-orange-50"
                  size="lg"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Retry Extraction
                </Button>
                <Button
                  className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600"
                  onClick={async () => {
                    await updateBoardingState({
                      companyId: params.companyId,
                      boardingStep: 3,
                    });
                    router.push(`/boarding/${params.companyId}/step3`);
                  }}
                  size="lg"
                >
                  <ArrowRight className="mr-2 h-4 w-4" />
                  Continue with Errors
                </Button>
              </div>
            ) : (
              <div className="flex w-full flex-col gap-3 sm:flex-row md:w-fit">
                <Button
                  variant="outline"
                  onClick={() => handleStartEtl(true)}
                  disabled={etlJobRunning}
                  className="border-blue-200 text-blue-700 hover:bg-blue-50"
                  size="lg"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Revalidate Data
                </Button>
                <Button
                  disabled={loading}
                  onClick={async () => {
                    setLoading(true);
                    await updateBoardingState({
                      companyId: params.companyId,
                      boardingStep: 3,
                    });
                    router.push(`/boarding/${params.companyId}/step3`);
                  }}
                  className="bg-gradient-to-r from-green-600 to-emerald-600 shadow-lg hover:from-green-700 hover:to-emerald-700"
                  size="lg"
                >
                  {loading ? (
                    <Loader className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <CheckCircle2 className="mr-2 h-4 w-4" />
                  )}
                  Continue to Review
                </Button>
              </div>
            )}
          </>
        );
      }}
      backButton={(step) => {
        return (
          <Button
            disabled={etlJobRunning}
            className="w-full md:w-fit"
            size="lg"
            variant="ghost"
            onClick={() => router.push("/boarding")}
          >
            ← Change Account
          </Button>
        );
      }}
    >
      <Step>
        <></>
      </Step>

      <Step>
        <Card className="border-0">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3 text-xl">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-r from-blue-500 to-indigo-600">
                <Database className="h-5 w-5 text-white" />
              </div>
              Data Extraction Process
            </CardTitle>
            <div className="space-y-3 text-gray-600">
              <p>We now need to extract data from your QuickBooks account.</p>
              <p className="text-sm">
                Below you can monitor the real-time status of each data type
                being processed:
              </p>
            </div>
          </CardHeader>

          <CardContent className="space-y-4">
            {[
              { key: "VENDORS", label: "Vendor Directory", icon: "🏢" },
              { key: "CUSTOMERS", label: "Customer Database", icon: "👥" },
              { key: "INVOICES", label: "Invoice History", icon: "📄" },
              { key: "BILLS", label: "Bills & Expenses", icon: "🧾" },
              { key: "ACCOUNTS", label: "Chart of Accounts", icon: "📊" },
              { key: "TERMS", label: "Payment Terms", icon: "⏰" },
              { key: "CLASSES", label: "Classes", icon: "🏷️" },
              { key: "ITEMS", label: "Items & Products", icon: "📦" },
              { key: "TAX_RATES", label: "Tax Rates", icon: "💸" },
              { key: "TAX_CODES", label: "Tax Codes", icon: "💸" },
            ].map(({ key, label, icon }) => {
              if (etlJobRunning) {
                const eStat = data?.[key] as
                  | {
                      status: ETLJobLogStatus;
                      success: boolean;
                      count: number;
                      message: string;
                      error?: string;
                    }
                  | undefined;

                return (
                  <EnhancedJobCard
                    key={key}
                    label={label}
                    icon={icon}
                    data={{
                      count: eStat?.count || 0,
                      entity: key,
                      error: eStat?.error || "",
                      message: eStat?.message || "",
                      status: (eStat?.status || "IDLE") as any,
                    }}
                  />
                );
              }

              const eStat = company?.etlJobs
                .find((job) => job.trigger === "BOARDING")
                ?.logs.find((log) => log.entity === key);

              return (
                <EnhancedJobCard
                  key={key}
                  label={label}
                  icon={icon}
                  data={{
                    count: eStat?.count || 0,
                    entity: key,
                    error: eStat?.error || "",
                    message: eStat?.message || "",
                    status: (eStat?.status || "IDLE") as any,
                  }}
                />
              );
            })}
          </CardContent>
        </Card>
      </Step>

      <Step>
        <></>
      </Step>
      <Step>
        <></>
      </Step>
    </Stepper>
  );
};

// Enhanced Job Card Component
const EnhancedJobCard = ({
  label,
  icon,
  data,
}: {
  label: string;
  icon: string;
  data: {
    count: number;
    entity: string;
    error: string;
    message: string;
    status: ETLJobLogStatus;
  };
}) => {
  const getStatusConfig = (status: ETLJobLogStatus) => {
    switch (status) {
      case "COMPLETED":
        return {
          color: "text-green-700",
          bg: "bg-green-50",
          border: "border-green-200",
          icon: <CheckCircle2 className="h-4 w-4 text-green-600" />,
          badge: "bg-green-100 text-green-800",
        };
      case "RUNNING":
        return {
          color: "text-blue-700",
          bg: "bg-blue-50",
          border: "border-blue-200",
          icon: <Loader className="h-4 w-4 animate-spin text-blue-600" />,
          badge: "bg-blue-100 text-blue-800",
        };
      case "FAILED":
        return {
          color: "text-red-700",
          bg: "bg-red-50",
          border: "border-red-200",
          icon: <AlertCircle className="h-4 w-4 text-red-600" />,
          badge: "bg-red-100 text-red-800",
        };
      default:
        return {
          color: "text-gray-700",
          bg: "bg-gray-50",
          border: "border-gray-200",
          icon: <Clock className="h-4 w-4 text-gray-500" />,
          badge: "bg-gray-100 text-gray-800",
        };
    }
  };

  const config = getStatusConfig(data.status);

  return (
    <div
      className={`rounded-lg border-2 p-4 transition-all duration-200 ${config.bg} ${config.border}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="text-2xl">{icon}</div>
          <div>
            <div className="font-semibold text-gray-900">{label}</div>
            <div className="text-sm text-gray-600">
              {data.message || `Processing ${label.toLowerCase()}...`}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {config.icon}
            <Badge className={config.badge}>{data.status.toLowerCase()}</Badge>
          </div>
        </div>
      </div>
      {data.error && (
        <div className="mt-3 rounded border border-red-200 bg-red-100 p-2 text-sm text-red-700">
          <strong>Error:</strong> {data.error}
        </div>
      )}
    </div>
  );
};

export default BoardingPage;
