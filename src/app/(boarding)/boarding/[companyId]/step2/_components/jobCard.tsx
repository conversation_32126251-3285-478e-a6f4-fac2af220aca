import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ert<PERSON><PERSON>cle,
  Clock,
  Refresh<PERSON><PERSON>,
  Loader,
} from "lucide-react";

type ETLStatus = "FAILED" | "COMPLETED" | "RUNNING" | "IDLE";
const StatusIcon = ({ status }: { status: ETLStatus }) => {
  const iconMap = {
    COMPLETED: <CheckCircle className="h-5 w-5 text-green-500" />,
    FAILED: <AlertCircle className="h-5 w-5 text-red-500" />,
    RUNNING: <RefreshCw className="h-5 w-5 animate-spin text-blue-500" />,
    IDLE: <Clock className="h-5 w-5 text-gray-500" />,
  };
  return iconMap[status] || iconMap.IDLE;
};

const getStatusTextColor = (status: ETLStatus) => {
  const colorMap = {
    COMPLETED: "text-green-500",
    FAILED: "text-red-500",
    RUNNING: "text-blue-500",
    IDLE: "text-gray-500",
  };
  return colorMap[status] || colorMap.IDLE;
};
const JobCard = ({
  data,
}: {
  data: {
    entity: string;
    status: ETLStatus;
    error: string;
    count: number;
    message: string;
  };
}) => {
  const { entity, status, error, count, message } = data;
  return (
    <div className="w-full space-y-4 rounded-lg border p-4">
      <div className="hidden items-center justify-between md:flex">
        <h3 className="text-lg font-medium">
          {entity}{" "}
          <small
            className={`text-xs ${status === "FAILED" ? "text-red-600" : "text-green-600"}`}
          >
            {error || message}
          </small>
        </h3>
        <div className="flex items-center gap-2">
          <div className={`text-sm font-medium ${getStatusTextColor(status)}`}>
            {status}
          </div>
          <StatusIcon status={status} />
        </div>
      </div>
      <div className="flex w-full flex-col justify-between gap-3 md:hidden">
        <div className="flex w-full items-center justify-between">
          <h3 className="text-lg font-medium">{entity}</h3>
          <div className="flex items-center gap-2">
            <div
              className={`text-sm font-medium ${getStatusTextColor(status)}`}
            >
              {status}
            </div>
            <StatusIcon status={status} />
          </div>
        </div>
        <small
          className={`text-xs ${status === "FAILED" ? "text-red-600" : "text-green-600"}`}
        >
          {error || message}
        </small>{" "}
      </div>
    </div>
  );
};

export default JobCard;
