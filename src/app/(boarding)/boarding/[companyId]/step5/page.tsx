import React from "react";
import Step5 from "./_components/page";
import { api } from "@/trpc/server";

const page = async ({ params }: { params: Promise<{ companyId: string }> }) => {
  const { companyId } = await params;
  const vendorsBoardingData = await api.quickbooks.vendorsBoarding({
    companyId: companyId,
  });
  console.log("vendorsBoardingData", vendorsBoardingData);

  const currency = await api.quickbooks.getCompanyCurrency({
    companyId: companyId,
  });
  const terms = await api.quickbooks.getAllTerms({
    companyId: companyId,
  });

  const last12MonthsVendors = await api.quickbooks.qbReport({
    companyId: companyId,
    url: "TransactionListByVendor?start_date=2024-05-01&end_date=2025-05-27",
  });

  return (
    <Step5
      currency={currency as any}
      terms={terms as any}
      vendorsBoardingData={vendorsBoardingData}
      activeVendors={last12MonthsVendors as any}
    />
  );
};

export default page;
