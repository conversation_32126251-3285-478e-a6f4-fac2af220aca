"use client";
import React, { useEffect, use<PERSON>emo, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import Stepper, { Step } from "@/components/ui/Stepper";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { api } from "@/trpc/react";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from "@/components/ui/card";
import { motion } from "framer-motion";

import {
  ArrowRight,
  AlertTriangle,
  CheckCircle,
  CircleCheckBig,
  Info,
  Loader,
  TriangleAlert,
  Users,
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@radix-ui/react-dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import type { TransactionListByVendorI } from "@/types/TransactionListByVendor";

const Step5 = ({
  currency,
  terms,
  vendorsBoardingData,
  activeVendors,
}: {
  currency: { name: string; value: string };
  terms: any[];
  vendorsBoardingData: any[];
  activeVendors: TransactionListByVendorI;
}) => {
  const router = useRouter();
  const params = useParams() as { companyId: string };
  const session = useSession();
  const [step, setStep] = useState(5);
  const [loading, setLoading] = useState(false);
  const activeVendorsList = useMemo(() => {
    return (
      activeVendors?.Rows?.Row?.map((r) => {
        return r.Header?.ColData?.[0]?.value;
      }) || []
    );
  }, [activeVendors]);
  const [vendorsToFix, setVendorsToFix] = useState<any[]>([]);
  useEffect(() => {
    if (vendorsBoardingData)
      setVendorsToFix(
        vendorsBoardingData
          ?.filter((c) => activeVendorsList.includes(c.DisplayName))
          ?.filter((c) => !c.TermRef)
          .map((v) => ({ ...v, TermRef: v.TermRef || v.mostUsedTerm })) || [],
      );
  }, [vendorsBoardingData]);

  useEffect(() => {
    console.log(activeVendorsList);
  }, [activeVendorsList]);

  // impelement fix all vendors
  const { mutate: fixAllVendorsInvoices, isPending: fixingVendorsStatus } =
    api.quickbooks.fixAllVendors.useMutation({
      onSuccess: () => {
        router.refresh();
      },
    });

  const { mutateAsync: updateBoardingState } =
    api.quickbooks.updateBoardingState.useMutation();

  const handleFixAllVendors = () => {
    fixAllVendorsInvoices({
      vendors: vendorsToFix.map((v) => ({
        vendorId: v.vendorId,
        TermRef: v.TermRef,
      })),
    });
  };

  if (session.status === "loading") {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-bold">Loading...</h2>
          <Progress className="mt-4 w-48" value={33} />
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full items-center justify-center p-4">
      <Stepper
        initialStep={step}
        // disableStepIndicators
        nextButton={(step) => {
          if (step === 6) {
            return (
              <Button
                className={`w-full bg-green-600 md:w-fit`}
                onClick={async () => {
                  router.push(`/company/${params.companyId}`);
                }}
              >
                <span className="relative z-10 flex items-center gap-2">
                  Go to Dashboard
                  <ArrowRight className="size-5 transition-transform duration-300 ease-out group-hover:translate-x-1" />
                </span>
                {/* <span className="absolute inset-0 -z-10 translate-y-[105%] bg-green-500 transition-transform duration-300 group-hover:translate-y-0" /> */}
              </Button>
            );
          }
          return (
            <Button
              disabled={loading}
              className={`w-full md:w-fit ${!!vendorsToFix.length && "bg-orange-500"}`}
              onClick={async () => {
                updateBoardingState({
                  companyId: params.companyId,
                  boardingStep: 6,
                });
                setStep(6);
              }}
            >
              {loading ? (
                <Loader className="animate-spin" />
              ) : vendorsToFix.length ? (
                "Continue with Issues"
              ) : loading ? (
                <Loader className="animate-spin" />
              ) : (
                "Next"
              )}
            </Button>
          );
        }}
        backButton={(step) => {
          return (
            <Button
              variant={"outline"}
              className="w-full md:w-fit"
              onClick={() => {
                const step = Number(
                  window.location.pathname.toString().slice(-1),
                );

                router.push(`/boarding/${params.companyId}/step${step - 1}`);
              }}
            >
              Back
            </Button>
          );
        }}
      >
        <Step>
          <></>
        </Step>
        <Step>
          <></>
        </Step>
        <Step>
          <></>
        </Step>
        <Step>
          <></>
        </Step>

        <Step>
          <div className="space-y-6">
            {/* Header */}
            <div className="space-y-2">
              <h2 className="text-2xl font-bold tracking-tight">
                Validation - Vendors
              </h2>
              <p className="text-muted-foreground">
                Please review and validate your vendors
              </p>
            </div>

            {/* Stats Overview */}
            <Card>
              <CardContent className="p-6">
                <div className="grid gap-6 md:grid-cols-3">
                  <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-2">
                      <Users className="text-muted-foreground h-5 w-5" />
                      <span className="text-sm font-medium">Total Vendors</span>
                    </div>
                    <p className="text-2xl font-bold">
                      {vendorsBoardingData?.length || 0}
                    </p>
                  </div>

                  <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-green-500" />
                      <span className="text-sm font-medium">
                        Active Vendors
                      </span>
                    </div>
                    <p className="text-2xl font-bold">
                      {vendorsBoardingData?.filter((c) =>
                        activeVendorsList.includes(c.DisplayName),
                      ).length || 0}
                    </p>
                  </div>

                  <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-5 w-5 text-orange-500" />
                      <span className="text-sm font-medium">
                        Vendors with Issues
                      </span>
                    </div>
                    <p className="text-2xl font-bold">
                      {vendorsBoardingData
                        ?.filter((c) =>
                          activeVendorsList.includes(c.DisplayName),
                        )
                        ?.filter((c) => !c.TermRef).length || 0}
                    </p>
                  </div>
                </div>
              </CardContent>
              {vendorsBoardingData
                ?.filter((c) => activeVendorsList.includes(c.DisplayName))
                ?.filter((c) => !c.TermRef).length ? (
                <CardFooter className="space-x-1.5 text-orange-600">
                  <div className="flex items-start gap-1.5">
                    <Info className="h-5 w-5 flex-shrink-0" />
                    <small>
                      Having all vendors with a term helps us to make the adding
                      of future payments easier. You can fix this by assigning
                      the most used term for each vendor.
                    </small>
                  </div>
                </CardFooter>
              ) : null}
            </Card>

            {/* Customer List */}
            {vendorsBoardingData?.filter((c) => c.avgMonthlyTotalSpent)
              .length ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Active Vendors</h3>
                  <div>
                    {/* FIXME */}
                    {vendorsToFix.length ? (
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            size="sm"
                            className="bg-orange-500 hover:bg-orange-600"
                            disabled={fixingVendorsStatus}
                          >
                            {fixingVendorsStatus ? (
                              <Loader className="animate-spin" />
                            ) : (
                              "Quick Fix All"
                            )}
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>
                              Fix Customers Invoices
                            </AlertDialogTitle>
                            <AlertDialogDescription>
                              Assign the most used class to all invoices not
                              mapped to a class
                            </AlertDialogDescription>
                            <div className="mt-2">
                              <div>
                                Set all vendors that doesn't have a term to
                              </div>
                              <div>
                                <Select
                                  onValueChange={(value) => {
                                    setVendorsToFix([
                                      ...vendorsToFix.map((vtf) => {
                                        if (!vtf.mostUsedTerm) {
                                          const termFound = terms?.find(
                                            (t) => t.Id === value,
                                          );
                                          if (termFound) {
                                            return {
                                              ...vtf,
                                              TermRef: {
                                                value: termFound?.Id,
                                              },
                                            };
                                          }
                                          return vtf;
                                        }
                                        return vtf;
                                      }),
                                    ]);
                                  }}
                                >
                                  <SelectTrigger className="w-[180px]">
                                    <SelectValue placeholder="Select Term" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {terms?.map((t) => (
                                      <SelectItem
                                        key={t.Id || `term-${t.Name}`}
                                        value={t?.Id || ""}
                                      >
                                        {t.Name}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>
                          </AlertDialogHeader>
                          <div className="max-h-[50dvh] space-y-3 overflow-y-auto">
                            {vendorsToFix.map((v, i) => (
                              <div
                                key={v.vendorId || `vendor-${i}`}
                                className={`rounded-lg border p-2 ${!v?.TermRef ? "border-l-2 border-l-red-500" : "border-l-2 border-l-green-500"}`}
                              >
                                <div className="line-clamp-1 font-bold">
                                  {v?.DisplayName || "N/A"}
                                </div>
                                <div className="flex items-center justify-between">
                                  <Label>
                                    Default term for the vendor will be{" "}
                                  </Label>
                                  <Select
                                    value={v.TermRef?.value || undefined}
                                    onValueChange={(value) => {
                                      setVendorsToFix([
                                        ...vendorsToFix.map((vtf) => {
                                          if (vtf.vendorId === v.vendorId) {
                                            const termFound = terms?.find(
                                              (t) => t.Id === value,
                                            );
                                            if (termFound) {
                                              return {
                                                ...vtf,
                                                TermRef: {
                                                  value: termFound?.Id,
                                                },
                                              };
                                            }
                                            return vtf;
                                          }
                                          return vtf;
                                        }),
                                      ]);
                                    }}
                                  >
                                    <SelectTrigger className="w-[180px]">
                                      <SelectValue placeholder="Select Term" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {terms?.map((t) => (
                                        <SelectItem
                                          key={t.Id || `term-option-${t.Name}`}
                                          value={t?.Id || ""}
                                        >
                                          {t.Name}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </div>
                              </div>
                            ))}
                          </div>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              className={`${vendorsToFix.some((c) => !c.TermRef) ? "" : "bg-green-500 hover:bg-green-600"}`}
                              disabled={vendorsToFix.some((c) => !c.TermRef)}
                              onClick={handleFixAllVendors}
                            >
                              FIX
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    ) : null}
                  </div>
                </div>

                <div className="max-h-[calc(100dvh-65dvh)] space-y-3 overflow-y-auto">
                  {vendorsBoardingData
                    ?.filter((c) => activeVendorsList.includes(c.DisplayName))
                    .map((vendor) => (
                      <Card
                        key={vendor.vendorId}
                        className={
                          !vendor.TermRef && !vendor.mostUsedTerm
                            ? "border-l-4 border-l-red-400"
                            : !vendor.TermRef
                              ? "border-l-4 border-l-orange-400"
                              : ""
                        }
                      >
                        <CardHeader className="p-2 pb-0 md:p-4">
                          <div className="flex flex-col justify-between gap-1.5 md:flex-row md:items-center md:gap-0">
                            <div className="flex items-center gap-2">
                              <h4 className="line-clamp-1 font-bold">
                                {vendor.DisplayName}
                              </h4>
                            </div>
                            <div>
                              <div className="flex items-center gap-1.5 text-sm font-medium">
                                Average spending / month{" "}
                                <p className="font-bold">
                                  {vendor.avgMonthlyTotalSpent.toLocaleString(
                                    "en-US",
                                    {
                                      minimumFractionDigits: 2,
                                      maximumFractionDigits: 2,
                                    },
                                  )}{" "}
                                  {currency?.value}
                                </p>
                              </div>
                              <div className="flex items-center gap-1.5 text-sm font-medium">
                                Balance:{" "}
                                <p className="font-bold">
                                  {vendor.Balance.toLocaleString("en-US", {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2,
                                  })}{" "}
                                  {currency?.value}
                                </p>
                              </div>
                            </div>
                          </div>
                        </CardHeader>

                        {!vendor.TermRef && !vendor.mostUsedTerm ? (
                          <>
                            <CardContent className="p-4 pt-2">
                              <div className="flex items-start gap-2 text-sm text-red-600">
                                <TriangleAlert className="mt-0.5 h-4 w-4" />
                                <span>
                                  Vendor doesn't have a term, and can't
                                  determine the most used term
                                </span>
                              </div>
                            </CardContent>
                          </>
                        ) : !vendor.TermRef ? (
                          <>
                            <CardContent className="p-4 pt-2">
                              <div className="flex items-start gap-2 text-sm text-orange-600">
                                <TriangleAlert className="mt-0.5 h-4 w-4" />
                                <span>Vendor doesn't have a term</span>
                              </div>

                              {true && (
                                <div className="mt-2 text-sm">
                                  <span className="text-muted-foreground">
                                    Most Used Term:
                                  </span>{" "}
                                  <span className="font-medium">
                                    {
                                      terms?.find(
                                        (t) =>
                                          t.Id == vendor?.mostUsedTerm?.value,
                                      )?.Name
                                    }
                                  </span>
                                </div>
                              )}
                            </CardContent>
                          </>
                        ) : null}
                      </Card>
                    ))}
                </div>
              </div>
            ) : null}
          </div>
        </Step>

        <Step>
          <div className="from-background to-background/80 relative flex min-h-[400px] w-full flex-col items-center justify-center overflow-hidden rounded-xl bg-gradient-to-b p-8">
            <div className="relative flex flex-col items-center justify-center gap-6 text-center">
              {/* Glowing background effect */}
              {/* <div className="absolute -top-10 h-40 w-40 rounded-full bg-green-500/20 blur-3xl" /> */}

              {/* Success icon with animations */}
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{
                  type: "spring",
                  stiffness: 200,
                  damping: 15,
                  delay: 0.2,
                }}
                className="relative"
              >
                <div className="absolute inset-0 animate-ping rounded-full bg-green-500/20" />
                <div className="relative flex items-center justify-center rounded-full bg-green-100 p-4 dark:bg-green-900/30">
                  <CheckCircle
                    className="size-24 text-green-500"
                    strokeWidth={1.5}
                  />
                </div>
              </motion.div>

              {/* Success text with animation */}
              <motion.div
                className="flex flex-col gap-2"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4 }}
              >
                <h2 className="text-3xl font-bold tracking-tight">
                  You're all set!
                </h2>
                <p className="text-muted-foreground">
                  Your setup is complete. You can now access all features.
                </p>
              </motion.div>
            </div>
          </div>
        </Step>
      </Stepper>
    </div>
  );
};

export default Step5;
