"use client";
import { api } from "@/trpc/react";
import React, { useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { Progress } from "@/components/ui/progress";

const Page = ({ children }: Readonly<{ children: React.ReactNode }>) => {
  const params = useParams() as { companyId: string };
  const router = useRouter();

  const {
    data: boardingState,
    refetch: refetchBoardingState,
    isLoading,
  } = api.quickbooks.getBoardingState.useQuery({
    companyId: params.companyId,
  });
  useEffect(() => {
    if (!isLoading)
      if (!boardingState) {
        // router.push(`/boarding/${params.companyId}/step2`);
      } else if (boardingState) {
        // if (boardingState.boardingStep === 6) {
        //   router.push(`/dashboard/${params.companyId}`);
        //   return;
        // }
        // router.push(
        //   `/boarding/${params.companyId}/step${boardingState.boardingStep}`,
        // );
      }
  }, [boardingState]);
  if (isLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-bold">Loading...</h2>
          <Progress className="mt-4 w-48" value={33} />
        </div>
      </div>
    );
  }
  return <>{children}</>;
};

export default Page;
