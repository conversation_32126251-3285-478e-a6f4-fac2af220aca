import React from "react";
import Step4 from "./_components/page";
import { api } from "@/trpc/server";

const page = async ({ params }: { params: Promise<{ companyId: string }> }) => {
  const { companyId } = await params;
  const classes = await api.quickbooks.getAllClasses({
    companyId: companyId,
  });
  const currency = await api.quickbooks.getCompanyCurrency({
    companyId: companyId,
  });
  const customersBoardingData = await api.quickbooks.customerBoarding({
    companyId: companyId,
  });

  const last12MonthsCustomers = await api.quickbooks.qbReport({
    companyId: companyId,
    url: "TransactionListByCustomer?start_date=2024-05-01&end_date=2025-05-29",
  });

  return (
    <Step4
      classes={classes as any}
      currency={currency as any}
      customersBoardingData={customersBoardingData as any}
      activeCustomers={last12MonthsCustomers as any}
    />
  );
};

export default page;
