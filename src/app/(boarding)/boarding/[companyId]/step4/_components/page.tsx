"use client";
import React, { useEffect, use<PERSON>emo, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import Stepper, { Step } from "@/components/ui/Stepper";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { api } from "@/trpc/react";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from "@/components/ui/card";
import {
  AlertTriangle,
  CheckCircle,
  Info,
  Loader,
  TriangleAlert,
  Users,
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@radix-ui/react-dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import type { ClassI } from "@/types/class";
import type { TransactionListByCustomerI } from "@/types/TransactionListByCustomer";

const Step4 = ({
  classes,
  currency,
  customersBoardingData,
  activeCustomers,
}: {
  classes: ClassI[];
  currency: { name: string; value: string };
  customersBoardingData: any[];
  activeCustomers: TransactionListByCustomerI;
}) => {
  const router = useRouter();
  const params = useParams() as { companyId: string };
  const [loading, setLoading] = useState(false);

  const [customersToFix, setCustomersToFix] = useState<any[]>([]);

  const activeCustomersList = useMemo(() => {
    return (
      activeCustomers?.Rows?.Row?.map((r) => {
        return r.Header?.ColData?.[0]?.value;
      }) || []
    );
  }, [activeCustomers]);

  useEffect(() => {
    if (customersBoardingData)
      setCustomersToFix(
        customersBoardingData
          ?.filter((c) => activeCustomersList.includes(c.name))
          ?.filter((c) => c.invoicesWithoutClass && c.totalMonthlyAvg) || [],
      );
  }, [customersBoardingData]);

  const { mutate: fixAllCustomerInvoices, isPending: fixingCustomersStatus } =
    api.quickbooks.fixAllCustomerInvoices.useMutation({
      onSuccess: (data) => {
        router.refresh();
      },
    });

  const { mutateAsync: updateBoardingState } =
    api.quickbooks.updateBoardingState.useMutation();

  const handleFixAllCustomers = () => {
    fixAllCustomerInvoices({
      customers: customersToFix.map((c) => ({
        customerId: c.customerId,
        class: c.mostusedclass,
      })),
    });
  };

  return (
    <div className="flex h-full items-center justify-center p-4">
      <Stepper
        disableStepIndicators
        initialStep={4}
        nextButton={(step) => {
          return (
            <Button
              disabled={loading}
              className={`w-full md:w-fit ${!!customersToFix.length && "bg-orange-500"}`}
              onClick={async () => {
                setLoading(true);
                await updateBoardingState({
                  companyId: params.companyId,
                  boardingStep: 5,
                });
                router.push(`/boarding/${params.companyId}/step5`);
              }}
            >
              {loading ? (
                <Loader className="animate-spin" />
              ) : customersToFix.length ? (
                "Continue with Issues"
              ) : loading ? (
                <Loader className="animate-spin" />
              ) : (
                "Next"
              )}
            </Button>
          );
        }}
        backButton={(step) => {
          return (
            <Button
              variant={"outline"}
              className="w-full md:w-fit"
              onClick={() => {
                const step = Number(
                  window.location.pathname.toString().slice(-1),
                );

                router.push(`/boarding/${params.companyId}/step${step - 1}`);
              }}
            >
              Back
            </Button>
          );
        }}
      >
        <Step>
          <></>
        </Step>
        <Step>
          <></>
        </Step>
        <Step>
          <></>
        </Step>

        <Step>
          <div className="space-y-6">
            {/* Header */}
            <div className="space-y-2">
              <h2 className="text-2xl font-bold tracking-tight">
                Validation - Customers
              </h2>
              <p className="text-muted-foreground">
                Please review and validate your customers
              </p>
            </div>

            {/* Stats Overview */}
            <Card>
              <CardContent className="p-6">
                <div className="grid gap-6 md:grid-cols-3">
                  <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-2">
                      <Users className="text-muted-foreground h-5 w-5" />
                      <span className="text-sm font-medium">
                        Total Customers
                      </span>
                    </div>
                    <p className="text-2xl font-bold">
                      {customersBoardingData?.length || 0}
                    </p>
                  </div>

                  <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-green-500" />
                      <span className="text-sm font-medium">
                        Active Customers
                      </span>
                    </div>
                    <p className="text-2xl font-bold">
                      {customersBoardingData?.filter((c) =>
                        activeCustomersList.includes(c.name),
                      ).length || 0}
                    </p>
                  </div>

                  <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-5 w-5 text-orange-500" />
                      <span className="text-sm font-medium">
                        Customers with Issues
                      </span>
                    </div>
                    <p className="text-2xl font-bold">
                      {customersBoardingData
                        ?.filter((c) => activeCustomersList.includes(c.name))
                        ?.filter((c) => c.invoicesWithoutClass).length || 0}
                    </p>
                  </div>
                </div>
              </CardContent>
              {customersBoardingData?.filter(
                (c) => c.invoicesWithoutClass && c.totalMonthlyAvg,
              ).length ? (
                <CardFooter className="space-x-1.5">
                  <div className="flex items-start gap-1.5 text-orange-600">
                    <Info className="h-5 w-5 flex-shrink-0" />
                    <small>
                      Having all invoices classified helps us to better
                      understand your revenue streams. You can fix this by
                      assigning the most used class to all invoices not mapped
                      to a class.
                    </small>
                  </div>
                  {/* {!classes.length ? (
                    <div className="flex items-start gap-1.5 text-red-600">
                      <Info className="h-5 w-5 flex-shrink-0" />
                      <small>
                        Having all invoices classified helps us to better
                        understand your revenue streams. You can fix this by
                        assigning the most used class to all invoices not mapped
                        to a class.
                      </small>
                    </div>
                  ) : null} */}
                </CardFooter>
              ) : null}
              {!classes.length ? (
                <CardFooter className="space-x-1.5">
                  <div className="flex items-start gap-1.5 text-red-600">
                    <TriangleAlert className="h-5 w-5 flex-shrink-0" />
                    <small>
                      You don't have any classes setup in QuickBooks.
                    </small>
                  </div>
                </CardFooter>
              ) : null}
            </Card>

            {/* Customer List */}
            {customersBoardingData?.filter((c) => c.totalMonthlyAvg).length ? (
              <div className="max-h-[calc(100dvh-60dvh)] space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Active Customers</h3>
                  <div>
                    {customersToFix.length ? (
                      <div>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              size="sm"
                              // variant={"outline"}
                              className="bg-orange-500 hover:bg-orange-600"
                              disabled={
                                fixingCustomersStatus || !classes.length
                              }
                            >
                              {fixingCustomersStatus ? (
                                <Loader className="animate-spin" />
                              ) : (
                                "Quick Fix All"
                              )}
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>
                                Fix Customers Invoices
                              </AlertDialogTitle>
                              <AlertDialogDescription>
                                Assign the most used class to all invoices not
                                mapped to a class
                              </AlertDialogDescription>
                              <AlertDialogDescription className=""></AlertDialogDescription>
                            </AlertDialogHeader>
                            <div className="max-h-[50dvh] space-y-3 overflow-y-auto">
                              {customersToFix
                                .sort((a, b) => {
                                  return (
                                    (!!a?.mostusedclass ? 1 : 0) -
                                    (!!b?.mostusedclass ? 1 : 0)
                                  );
                                })
                                .map((c) => (
                                  <div
                                    key={c.customerId}
                                    className={`rounded-lg border p-2 ${!c?.mostusedclass ? "border-l-2 border-l-red-500" : "border-l-2 border-l-green-500"}`}
                                  >
                                    <p className="line-clamp-1 font-bold">
                                      {c?.name || "N/A"}
                                    </p>
                                    <div className="flex items-center justify-between">
                                      <Label>
                                        {c.invoicesWithoutClass} Invoices will
                                        be mapped to{" "}
                                      </Label>
                                      <Select
                                        value={
                                          c.mostusedclass?.value || undefined
                                        }
                                        onValueChange={(value) => {
                                          setCustomersToFix([
                                            ...customersToFix.map((ctf) => {
                                              if (
                                                ctf.customerId === c.customerId
                                              ) {
                                                const classFound =
                                                  classes?.find(
                                                    (cc) => cc.Id === value,
                                                  );
                                                if (classFound) {
                                                  return {
                                                    ...ctf,
                                                    mostusedclass: {
                                                      value: classFound?.Id,
                                                      name: classFound?.FullyQualifiedName,
                                                    },
                                                  };
                                                }
                                                return ctf;
                                              }
                                              return ctf;
                                            }),
                                          ]);
                                        }}
                                      >
                                        <SelectTrigger className="w-[180px]">
                                          <SelectValue placeholder="Select Class" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          {classes?.map((c) => (
                                            <SelectItem
                                              key={c.Id}
                                              value={c?.Id || ""}
                                            >
                                              {c.FullyQualifiedName}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    </div>
                                  </div>
                                ))}
                            </div>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                className={`${
                                  customersToFix.some((c) => !c.mostusedclass)
                                    ? ""
                                    : "bg-green-500 hover:bg-green-600"
                                }`}
                                disabled={customersToFix.some(
                                  (c) => !c.mostusedclass,
                                )}
                                onClick={handleFixAllCustomers}
                              >
                                FIX
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    ) : null}
                  </div>
                </div>

                <div className="max-h-[50dvh] space-y-3 overflow-y-auto">
                  {customersBoardingData
                    ?.filter((c) => activeCustomersList.includes(c.name))
                    ?.filter((c) => c.totalMonthlyAvg)
                    .map((customer) => (
                      <Card
                        key={customer.customerId}
                        className={
                          customer.invoicesWithoutClass
                            ? "border-l-4 border-l-orange-400"
                            : ""
                        }
                      >
                        <CardHeader className="p-4 pb-0">
                          <div className="flex flex-col justify-between md:flex-row md:items-center md:gap-0">
                            <div className="flex items-center gap-2">
                              <h4 className="line-clamp-1 font-bold">
                                {customer.name}
                              </h4>
                            </div>
                            <div className="flex gap-1.5 text-sm font-medium whitespace-nowrap">
                              average sales / month {""}
                              <p className="font-bold">
                                {customer.totalMonthlyAvg.toLocaleString(
                                  "en-US",
                                  {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2,
                                  },
                                )}{" "}
                                {currency.value}
                              </p>
                            </div>
                          </div>
                        </CardHeader>

                        {customer.invoicesWithoutClass ? (
                          <>
                            <CardContent className="px-4">
                              <div className="flex items-start gap-2 text-sm text-orange-600">
                                <TriangleAlert className="mt-0.5 h-4 w-4" />
                                <span>
                                  {customer.invoicesWithoutClass} invoices are
                                  not classified in QuickBooks
                                </span>
                              </div>

                              {customer.mostusedclass && (
                                <div className="mt-2 text-sm">
                                  <span className="text-muted-foreground">
                                    Most Used Class:
                                  </span>{" "}
                                  <span className="font-medium">
                                    {customer.mostusedclass.name}
                                  </span>
                                </div>
                              )}
                            </CardContent>
                            {/* <CardFooter className="p-4 pt-0">
                          {customer.mostusedclass ? (
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  size="sm"
                                  className="bg-orange-500 hover:bg-orange-600"
                                >
                                  Quick Fix
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>
                                    Are you absolutely sure?
                                  </AlertDialogTitle>
                                  <AlertDialogDescription className="">
                                    We will map all (
                                    {customer?.invoicesWithoutClass}) invoices
                                    for{" "}
                                    <span className="font-bold">
                                      {customer.name}
                                    </span>{" "}
                                    to the most used class:{" "}
                                    <span className="font-bold">
                                      {customer.mostusedclass.name}
                                    </span>
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction className="bg-green-500 hover:bg-green-600">
                                    Continue
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          ) : (
                            <Button size="sm" variant="destructive">
                              Needs Manual Fix
                            </Button>
                          )}
                        </CardFooter> */}
                          </>
                        ) : null}
                      </Card>
                    ))}
                </div>
              </div>
            ) : null}
          </div>
        </Step>
        <Step>
          <></>
        </Step>
        <Step>
          <></>
        </Step>
      </Stepper>
    </div>
  );
};

export default Step4;
