import React from "react";
import Step3 from "./_components/page";
import { api } from "@/trpc/server";

const page = async ({ params }: { params: Promise<{ companyId: string }> }) => {
  const { companyId } = await params;
  const finalCustomReport = await api.quickbooks.finalReport({
    companyId: companyId,
  });

  return (
    <Step3
      finalCustomReport={finalCustomReport as any}
      // finalCustomReport={[
      //   {
      //     Id: "223",
      //     Name: "Accounting Services",
      //     SubAccount: false,
      //     FullyQualifiedName: "Accounting Services",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "LegalProfessionalFees",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 16000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "102",
      //     Name: "Accounts Payable (A/P)",
      //     SubAccount: false,
      //     FullyQualifiedName: "Accounts Payable (A/P)",
      //     Active: true,
      //     Classification: "Liability",
      //     AccountType: "Accounts Payable",
      //     AccountSubType: "AccountsPayable",
      //     CurrentBalance: -546375.06,
      //     CurrentBalanceWithSubAccounts: -546375.06,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 546375.06,
      //     include: true,
      //   },
      //   {
      //     Id: "132",
      //     Name: "Accounts Receivable (A/R)",
      //     SubAccount: false,
      //     FullyQualifiedName: "Accounts Receivable (A/R)",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Accounts Receivable",
      //     AccountSubType: "AccountsReceivable",
      //     CurrentBalance: 992676.35,
      //     CurrentBalanceWithSubAccounts: 992676.35,
      //     ParentRef: null,
      //     debit: 829422.25,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "29",
      //     Name: "Accrued holiday payable",
      //     SubAccount: false,
      //     FullyQualifiedName: "Accrued holiday payable",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Long Term Liability",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "31",
      //     Name: "Accrued liabilities",
      //     SubAccount: false,
      //     FullyQualifiedName: "Accrued liabilities",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Other Current Liability",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "30",
      //     Name: "Accrued non-current liabilities",
      //     SubAccount: false,
      //     FullyQualifiedName: "Accrued non-current liabilities",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Long Term Liability",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "45",
      //     Name: "Accumulated Depreciation",
      //     SubAccount: false,
      //     FullyQualifiedName: "Accumulated Depreciation",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Fixed Asset",
      //     AccountSubType: "AccumulatedDepreciation",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: -827449.19,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "237",
      //     Name: "CCTV Cameras",
      //     SubAccount: true,
      //     FullyQualifiedName: "Accumulated Depreciation:CCTV Cameras",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Fixed Asset",
      //     AccountSubType: "AccumulatedDepreciation",
      //     CurrentBalance: -10505.33,
      //     CurrentBalanceWithSubAccounts: -10505.33,
      //     ParentRef: { value: "45" },
      //     debit: 0,
      //     credit: 10505.33,
      //     include: true,
      //   },
      //   {
      //     Id: "321",
      //     Name: "Computer and Office Equipment",
      //     SubAccount: true,
      //     FullyQualifiedName:
      //       "Accumulated Depreciation:Computer and Office Equipment",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Fixed Asset",
      //     AccountSubType: "AccumulatedDepreciation",
      //     CurrentBalance: -4641.49,
      //     CurrentBalanceWithSubAccounts: -4641.49,
      //     ParentRef: { value: "45" },
      //     debit: 0,
      //     credit: 4641.49,
      //     include: true,
      //   },
      //   {
      //     Id: "236",
      //     Name: "Fit Out",
      //     SubAccount: true,
      //     FullyQualifiedName: "Accumulated Depreciation:Fit Out",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Fixed Asset",
      //     AccountSubType: "AccumulatedDepreciation",
      //     CurrentBalance: -384059.42,
      //     CurrentBalanceWithSubAccounts: -384059.42,
      //     ParentRef: { value: "45" },
      //     debit: 0,
      //     credit: 384059.42,
      //     include: true,
      //   },
      //   {
      //     Id: "238",
      //     Name: "Kitchen Equipment",
      //     SubAccount: true,
      //     FullyQualifiedName: "Accumulated Depreciation:Kitchen Equipment",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Fixed Asset",
      //     AccountSubType: "AccumulatedDepreciation",
      //     CurrentBalance: -424242.95,
      //     CurrentBalanceWithSubAccounts: -424242.95,
      //     ParentRef: { value: "45" },
      //     debit: 0,
      //     credit: 424242.95,
      //     include: true,
      //   },
      //   {
      //     Id: "235",
      //     Name: "Signage",
      //     SubAccount: true,
      //     FullyQualifiedName: "Accumulated Depreciation:Signage",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Fixed Asset",
      //     AccountSubType: "AccumulatedDepreciation",
      //     CurrentBalance: -4000,
      //     CurrentBalanceWithSubAccounts: -4000,
      //     ParentRef: { value: "45" },
      //     debit: 0,
      //     credit: 4000,
      //     include: true,
      //   },
      //   {
      //     Id: "339",
      //     Name: "Additional Fee Deliveroo",
      //     SubAccount: false,
      //     FullyQualifiedName: "Additional Fee Deliveroo",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 9498.69,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "155",
      //     Name: "Admin Fee on Salik",
      //     SubAccount: false,
      //     FullyQualifiedName: "Admin Fee on Salik",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "268",
      //     Name: "Administrative Salaries",
      //     SubAccount: false,
      //     FullyQualifiedName: "Administrative Salaries",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "PayrollExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "125",
      //     Name: "Advance to Staff",
      //     SubAccount: false,
      //     FullyQualifiedName: "Advance to Staff",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "EmployeeCashAdvances",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 6330.5,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "207",
      //     Name: "Abdul Ghaffar Advance",
      //     SubAccount: true,
      //     FullyQualifiedName: "Advance to Staff:Abdul Ghaffar Advance",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "EmployeeCashAdvances",
      //     CurrentBalance: 430.5,
      //     CurrentBalanceWithSubAccounts: 430.5,
      //     ParentRef: { value: "125" },
      //     debit: 430.5,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "290",
      //     Name: "Advance to Sufyan - Driver",
      //     SubAccount: true,
      //     FullyQualifiedName: "Advance to Staff:Advance to Sufyan - Driver",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "OtherCurrentAssets",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "125" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "188",
      //     Name: "Aehtizaz Mehdi",
      //     SubAccount: true,
      //     FullyQualifiedName: "Advance to Staff:Aehtizaz Mehdi",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "OtherCurrentAssets",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "125" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "164",
      //     Name: "Amanat Advance",
      //     SubAccount: true,
      //     FullyQualifiedName: "Advance to Staff:Amanat Advance",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "OtherCurrentAssets",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "125" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "126",
      //     Name: "Arun Advance",
      //     SubAccount: true,
      //     FullyQualifiedName: "Advance to Staff:Arun Advance",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "EmployeeCashAdvances",
      //     CurrentBalance: 5900,
      //     CurrentBalanceWithSubAccounts: 5900,
      //     ParentRef: { value: "125" },
      //     debit: 5900,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "337",
      //     Name: "Ashwani Kumar",
      //     SubAccount: true,
      //     FullyQualifiedName: "Advance to Staff:Ashwani Kumar",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "EmployeeCashAdvances",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "125" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "349",
      //     Name: "Khursheed Ahmed",
      //     SubAccount: true,
      //     FullyQualifiedName: "Advance to Staff:Khursheed Ahmed",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "EmployeeCashAdvances",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "125" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "340",
      //     Name: "Naveed Advance",
      //     SubAccount: true,
      //     FullyQualifiedName: "Advance to Staff:Naveed Advance",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "EmployeeCashAdvances",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "125" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "206",
      //     Name: "Other current assets",
      //     SubAccount: true,
      //     FullyQualifiedName: "Advance to Staff:Other current assets",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "OtherCurrentAssets",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "125" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "**********",
      //     Name: "Ravi Baddula",
      //     SubAccount: true,
      //     FullyQualifiedName: "Advance to Staff:Ravi Baddula",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "EmployeeCashAdvances",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "125" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "306",
      //     Name: "Umar Bilal",
      //     SubAccount: true,
      //     FullyQualifiedName: "Advance to Staff:Umar Bilal",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "EmployeeCashAdvances",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "125" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "307",
      //     Name: "Zameer Hussain",
      //     SubAccount: true,
      //     FullyQualifiedName: "Advance to Staff:Zameer Hussain",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "EmployeeCashAdvances",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "125" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "162",
      //     Name: "Advance to Suppliers",
      //     SubAccount: false,
      //     FullyQualifiedName: "Advance to Suppliers",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "OtherCurrentAssets",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "182",
      //     Name: "Advances",
      //     SubAccount: false,
      //     FullyQualifiedName: "Advances",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "OtherCurrentAssets",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "249",
      //     Name: "Advertisement Fee Yearly",
      //     SubAccount: false,
      //     FullyQualifiedName: "Advertisement Fee Yearly",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "131",
      //     Name: "Advertising/Promotional",
      //     SubAccount: false,
      //     FullyQualifiedName: "Advertising/Promotional",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "**********",
      //     Name: "Ahsan Shb (Sats)",
      //     SubAccount: false,
      //     FullyQualifiedName: "Ahsan Shb (Sats)",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Other Current Liability",
      //     AccountSubType: null,
      //     CurrentBalance: -500,
      //     CurrentBalanceWithSubAccounts: -500,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 500,
      //     include: false,
      //   },
      //   {
      //     Id: "90",
      //     Name: "Air Tickets",
      //     SubAccount: false,
      //     FullyQualifiedName: "Air Tickets",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "106",
      //     Name: "Al Ansari Exchange Charges",
      //     SubAccount: false,
      //     FullyQualifiedName: "Al Ansari Exchange Charges",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 675,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "69",
      //     Name: "Allowance for bad debt",
      //     SubAccount: false,
      //     FullyQualifiedName: "Allowance for bad debt",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "AllowanceForBadDebts",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "258",
      //     Name: "Amazon Web Services",
      //     SubAccount: false,
      //     FullyQualifiedName: "Amazon Web Services",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "OfficeGeneralAdministrativeExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "26",
      //     Name: "Amortisation expense",
      //     SubAccount: false,
      //     FullyQualifiedName: "Amortisation expense",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "187",
      //     Name: "Artisan Sourdough Tag",
      //     SubAccount: false,
      //     FullyQualifiedName: "Artisan Sourdough Tag",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "SuppliesMaterialsCogs",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "317",
      //     Name: "Audit Expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Audit Expenses",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "LegalProfessionalFees",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "12",
      //     Name: "Available for sale assets (short-term)",
      //     SubAccount: false,
      //     FullyQualifiedName: "Available for sale assets (short-term)",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Other Current Asset",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "246",
      //     Name: "Bad debts",
      //     SubAccount: false,
      //     FullyQualifiedName: "Bad debts",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "BadDebts",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 750.93,
      //     credit: 0,
      //     include: true,
      //     total: 74.42,
      //     monthlySeries: [{ month: "May 2025", value: 74.42 }],
      //     avg_monthly: 74.42,
      //   },
      //   {
      //     Id: "48",
      //     Name: "Bank charges",
      //     SubAccount: false,
      //     FullyQualifiedName: "Bank charges",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "BankCharges",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 1637.55,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "293",
      //     Name: "Billable Expense",
      //     SubAccount: false,
      //     FullyQualifiedName: "Billable Expense",
      //     Active: true,
      //     Classification: "Revenue",
      //     AccountType: "Income",
      //     AccountSubType: "SalesOfProductIncome",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "80",
      //     Name: "Billable Expense Income",
      //     SubAccount: false,
      //     FullyQualifiedName: "Billable Expense Income",
      //     Active: true,
      //     Classification: "Revenue",
      //     AccountType: "Income",
      //     AccountSubType: "SalesOfProductIncome",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "300",
      //     Name: "Block charges",
      //     SubAccount: false,
      //     FullyQualifiedName: "Block charges",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "172",
      //     Name: "Bourgeois Flour Consumption",
      //     SubAccount: false,
      //     FullyQualifiedName: "Bourgeois Flour Consumption",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "SuppliesMaterialsCogs",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 550597.26,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "242",
      //     Name: "Brainstorm Workshop",
      //     SubAccount: false,
      //     FullyQualifiedName: "Brainstorm Workshop",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "221",
      //     Name: "Branding",
      //     SubAccount: false,
      //     FullyQualifiedName: "Branding",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "166",
      //     Name: "Business Insurance",
      //     SubAccount: false,
      //     FullyQualifiedName: "Business Insurance",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "Insurance",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 3158.46,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "**********",
      //     Name: "Business Revaluation",
      //     SubAccount: false,
      //     FullyQualifiedName: "Business Revaluation",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 4000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "189",
      //     Name: "Canva",
      //     SubAccount: false,
      //     FullyQualifiedName: "Canva",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "209",
      //     Name: "Car Wash Expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Car Wash Expenses",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "RepairMaintenance",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "86",
      //     Name: "Cash on hand",
      //     SubAccount: false,
      //     FullyQualifiedName: "Cash on hand",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Bank",
      //     AccountSubType: "CashOnHand",
      //     CurrentBalance: 18159.53,
      //     CurrentBalanceWithSubAccounts: 17769.22,
      //     ParentRef: null,
      //     debit: 17599.09,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "163",
      //     Name: "Cash With Ahsan",
      //     SubAccount: true,
      //     FullyQualifiedName: "Cash on hand:Cash With Ahsan",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Bank",
      //     AccountSubType: "CashOnHand",
      //     CurrentBalance: -389.31,
      //     CurrentBalanceWithSubAccounts: -390.31,
      //     ParentRef: { value: "86" },
      //     debit: 0,
      //     credit: 389.31,
      //     include: false,
      //   },
      //   {
      //     Id: "232",
      //     Name: "Cash With Rami",
      //     SubAccount: true,
      //     FullyQualifiedName: "Cash on hand:Cash With Ahsan:Cash With Rami",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Bank",
      //     AccountSubType: "CashOnHand",
      //     CurrentBalance: -1,
      //     CurrentBalanceWithSubAccounts: -1,
      //     ParentRef: { value: "163" },
      //     debit: 0,
      //     credit: 1,
      //     include: false,
      //   },
      //   {
      //     Id: "266",
      //     Name: "Cash with Tmam",
      //     SubAccount: false,
      //     FullyQualifiedName: "Cash with Tmam",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Bank",
      //     AccountSubType: "CashOnHand",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "292",
      //     Name: "Certification and Compliance Expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Certification and Compliance Expenses",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "LegalProfessionalFees",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 2160,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "39",
      //     Name: "Change in inventory - COS",
      //     SubAccount: false,
      //     FullyQualifiedName: "Change in inventory - COS",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "332",
      //     Name: "Chaser",
      //     SubAccount: false,
      //     FullyQualifiedName: "Chaser",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 2884.73,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "**********",
      //     Name: "Claude AI",
      //     SubAccount: false,
      //     FullyQualifiedName: "Claude AI",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "OfficeGeneralAdministrativeExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 984.01,
      //     credit: 0,
      //     include: true,
      //     total: 529.47,
      //     monthlySeries: [{ month: "May 2025", value: 529.47 }],
      //     avg_monthly: 529.47,
      //   },
      //   {
      //     Id: "201",
      //     Name: "Cleaning Stuff",
      //     SubAccount: false,
      //     FullyQualifiedName: "Cleaning Stuff",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "OfficeGeneralAdministrativeExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 3672.38,
      //     credit: 0,
      //     include: true,
      //     total: 1081.46,
      //     monthlySeries: [{ month: "May 2025", value: 1081.46 }],
      //     avg_monthly: 1081.46,
      //   },
      //   {
      //     Id: "**********",
      //     Name: "Commission Deliveroo",
      //     SubAccount: false,
      //     FullyQualifiedName: "Commission Deliveroo",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 37047.72,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "315",
      //     Name: "Commission Income",
      //     SubAccount: false,
      //     FullyQualifiedName: "Commission Income",
      //     Active: true,
      //     Classification: "Revenue",
      //     AccountType: "Income",
      //     AccountSubType: "SalesOfProductIncome",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 12780,
      //     include: true,
      //   },
      //   {
      //     Id: "27",
      //     Name: "Commissions and fees",
      //     SubAccount: false,
      //     FullyQualifiedName: "Commissions and fees",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 55022.93,
      //     credit: 0,
      //     include: true,
      //     total: 12762.57,
      //     monthlySeries: [{ month: "May 2025", value: 12762.57 }],
      //     avg_monthly: 12762.57,
      //   },
      //   {
      //     Id: "83",
      //     Name: "Cost of sales",
      //     SubAccount: false,
      //     FullyQualifiedName: "Cost of sales",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "SuppliesMaterialsCogs",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 228.57,
      //     credit: 0,
      //     include: false,
      //     total: 228.57,
      //     monthlySeries: [{ month: "May 2025", value: 228.57 }],
      //     avg_monthly: 228.57,
      //   },
      //   {
      //     Id: "219",
      //     Name: "Country Bread Stickers",
      //     SubAccount: false,
      //     FullyQualifiedName: "Country Bread Stickers",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "SuppliesMaterialsCogs",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "210",
      //     Name: "Covid - 19 PCR Test",
      //     SubAccount: false,
      //     FullyQualifiedName: "Covid - 19 PCR Test",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "LegalProfessionalFees",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "256",
      //     Name: "Customer Engagement Fees",
      //     SubAccount: false,
      //     FullyQualifiedName: "Customer Engagement Fees",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 374.82,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "214",
      //     Name: "Damage & Expire Stock",
      //     SubAccount: false,
      //     FullyQualifiedName: "Damage & Expire Stock",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "SuppliesMaterialsCogs",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 253.58,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "352",
      //     Name: "Deferred Revenue",
      //     SubAccount: false,
      //     FullyQualifiedName: "Deferred Revenue",
      //     Active: true,
      //     Classification: "Liability",
      //     AccountType: "Other Current Liability",
      //     AccountSubType: "DeferredRevenue",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "61",
      //     Name: "Deferred tax assets",
      //     SubAccount: false,
      //     FullyQualifiedName: "Deferred tax assets",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Other Asset",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "100",
      //     Name: "Depreciation",
      //     SubAccount: false,
      //     FullyQualifiedName: "Depreciation",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Other Expense",
      //     AccountSubType: "Depreciation",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "122",
      //     Name: "Dewa Expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Dewa Expenses",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "Utilities",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 55205.93,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "94",
      //     Name: "Dewa Power Upgrade",
      //     SubAccount: false,
      //     FullyQualifiedName: "Dewa Power Upgrade",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "RepairMaintenance",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "216",
      //     Name: "Digital Ocean",
      //     SubAccount: false,
      //     FullyQualifiedName: "Digital Ocean",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 568.98,
      //     credit: 0,
      //     include: true,
      //     total: 113.8,
      //     monthlySeries: [{ month: "May 2025", value: 113.8 }],
      //     avg_monthly: 113.8,
      //   },
      //   {
      //     Id: "133",
      //     Name: "Discounts given",
      //     SubAccount: false,
      //     FullyQualifiedName: "Discounts given",
      //     Active: true,
      //     Classification: "Revenue",
      //     AccountType: "Income",
      //     AccountSubType: "DiscountsRefundsGiven",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 4447.89,
      //     credit: 0,
      //     include: true,
      //     total: -866.38,
      //     monthlySeries: [{ month: "May 2025", value: -866.38 }],
      //     avg_monthly: -866.38,
      //   },
      //   {
      //     Id: "38",
      //     Name: "Discounts given - COS",
      //     SubAccount: false,
      //     FullyQualifiedName: "Discounts given - COS",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "252",
      //     Name: "Display Fees",
      //     SubAccount: false,
      //     FullyQualifiedName: "Display Fees",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 1183.94,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "11",
      //     Name: "Dividend disbursed",
      //     SubAccount: false,
      //     FullyQualifiedName: "Dividend disbursed",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Equity",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "59",
      //     Name: "Dividend income",
      //     SubAccount: false,
      //     FullyQualifiedName: "Dividend income",
      //     Active: true,
      //     Classification: "Revenue",
      //     AccountType: "Other Income",
      //     AccountSubType: "DividendIncome",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "37",
      //     Name: "Dividends payable",
      //     SubAccount: false,
      //     FullyQualifiedName: "Dividends payable",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Other Current Liability",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "262",
      //     Name: "Dropbox",
      //     SubAccount: false,
      //     FullyQualifiedName: "Dropbox",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "OfficeGeneralAdministrativeExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 716.2,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "167",
      //     Name: "Dubai Real Estate Exp",
      //     SubAccount: false,
      //     FullyQualifiedName: "Dubai Real Estate Exp",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "RentOrLeaseOfBuildings",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 10813,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "49",
      //     Name: "Dues and subscriptions",
      //     SubAccount: false,
      //     FullyQualifiedName: "Dues and subscriptions",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "DuesSubscriptions",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 13697.78,
      //     credit: 0,
      //     include: true,
      //     total: 1371.71,
      //     monthlySeries: [{ month: "May 2025", value: 1371.71 }],
      //     avg_monthly: 1371.71,
      //   },
      //   {
      //     Id: "257",
      //     Name: "Ecommerce Fees",
      //     SubAccount: false,
      //     FullyQualifiedName: "Ecommerce Fees",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 374.95,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "326",
      //     Name: "End of Services Benifits",
      //     SubAccount: false,
      //     FullyQualifiedName: "End of Services Benifits",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Other Expense",
      //     AccountSubType: "OtherMiscellaneousExpense",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "173",
      //     Name: "Equipment Expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Equipment Expenses",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "RepairMaintenance",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 7211.28,
      //     credit: 0,
      //     include: true,
      //     total: 2014.24,
      //     monthlySeries: [{ month: "May 2025", value: 2014.24 }],
      //     avg_monthly: 2014.24,
      //   },
      //   {
      //     Id: "50",
      //     Name: "Equipment rental",
      //     SubAccount: false,
      //     FullyQualifiedName: "Equipment rental",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "EquipmentRental",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "17",
      //     Name: "Equity in earnings of subsidiaries",
      //     SubAccount: false,
      //     FullyQualifiedName: "Equity in earnings of subsidiaries",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Equity",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "168",
      //     Name: "Fire Safety Maintenance Exp",
      //     SubAccount: false,
      //     FullyQualifiedName: "Fire Safety Maintenance Exp",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "OtherCostsOfServiceCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "143",
      //     Name: "Food Tasting",
      //     SubAccount: false,
      //     FullyQualifiedName: "Food Tasting",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "SuppliesMaterialsCogs",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "68",
      //     Name: "Freight and delivery - COS",
      //     SubAccount: false,
      //     FullyQualifiedName: "Freight and delivery - COS",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "ShippingFreightDeliveryCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "135",
      //     Name: "Google Cloud - GSuite",
      //     SubAccount: false,
      //     FullyQualifiedName: "Google Cloud - GSuite",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "LegalProfessionalFees",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 7579.09,
      //     credit: 0,
      //     include: true,
      //     total: 298.62,
      //     monthlySeries: [{ month: "May 2025", value: 298.62 }],
      //     avg_monthly: 298.62,
      //   },
      //   {
      //     Id: "213",
      //     Name: "Gratuity Expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Gratuity Expenses",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 18977.64,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "224",
      //     Name: "HACCP Certification",
      //     SubAccount: false,
      //     FullyQualifiedName: "HACCP Certification",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "LegalProfessionalFees",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "21",
      //     Name: "Income tax expense",
      //     SubAccount: false,
      //     FullyQualifiedName: "Income tax expense",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "66",
      //     Name: "Insurance - Disability",
      //     SubAccount: false,
      //     FullyQualifiedName: "Insurance - Disability",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "Insurance",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "36",
      //     Name: "Insurance - General",
      //     SubAccount: false,
      //     FullyQualifiedName: "Insurance - General",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "Insurance",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "65",
      //     Name: "Insurance - Liability",
      //     SubAccount: false,
      //     FullyQualifiedName: "Insurance - Liability",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "Insurance",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "34",
      //     Name: "Intangibles",
      //     SubAccount: false,
      //     FullyQualifiedName: "Intangibles",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Other Asset",
      //     AccountSubType: null,
      //     CurrentBalance: 11055,
      //     CurrentBalanceWithSubAccounts: 11055,
      //     ParentRef: null,
      //     debit: 11055,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "324",
      //     Name: "Interest earned",
      //     SubAccount: false,
      //     FullyQualifiedName: "Interest earned",
      //     Active: true,
      //     Classification: "Revenue",
      //     AccountType: "Other Income",
      //     AccountSubType: "InterestEarned",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "51",
      //     Name: "Interest expense",
      //     SubAccount: false,
      //     FullyQualifiedName: "Interest expense",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "InterestPaid",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "60",
      //     Name: "Interest income",
      //     SubAccount: false,
      //     FullyQualifiedName: "Interest income",
      //     Active: true,
      //     Classification: "Revenue",
      //     AccountType: "Other Income",
      //     AccountSubType: "InterestEarned",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "113",
      //     Name: "Internet & Telephone Expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Internet & Telephone Expenses",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "Utilities",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 9680,
      //     credit: 0,
      //     include: true,
      //     total: 900,
      //     monthlySeries: [{ month: "May 2025", value: 900 }],
      //     avg_monthly: 900,
      //   },
      //   {
      //     Id: "119",
      //     Name: "*********",
      //     SubAccount: true,
      //     FullyQualifiedName: "Internet & Telephone Expenses:*********",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "Utilities",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "113" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "118",
      //     Name: "*********",
      //     SubAccount: true,
      //     FullyQualifiedName: "Internet & Telephone Expenses:*********",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "Utilities",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "113" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "153",
      //     Name: "Intrest Income",
      //     SubAccount: false,
      //     FullyQualifiedName: "Intrest Income",
      //     Active: true,
      //     Classification: "Revenue",
      //     AccountType: "Other Income",
      //     AccountSubType: "InterestEarned",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 21.74,
      //     include: false,
      //   },
      //   {
      //     Id: "64",
      //     Name: "Inventory",
      //     SubAccount: false,
      //     FullyQualifiedName: "Inventory",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "SuppliesMaterialsCogs",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //     total: 0,
      //     monthlySeries: [{ month: "May 2025", value: 0 }],
      //     avg_monthly: 0,
      //   },
      //   {
      //     Id: "186",
      //     Name: "Inventory - Packing Material",
      //     SubAccount: false,
      //     FullyQualifiedName: "Inventory - Packing Material",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "Inventory",
      //     CurrentBalance: 21200.38,
      //     CurrentBalanceWithSubAccounts: 21200.38,
      //     ParentRef: null,
      //     debit: 21200.38,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "84",
      //     Name: "Inventory Asset",
      //     SubAccount: false,
      //     FullyQualifiedName: "Inventory Asset",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "Inventory",
      //     CurrentBalance: 259235.23,
      //     CurrentBalanceWithSubAccounts: 259235.23,
      //     ParentRef: null,
      //     debit: 259235.23,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "117",
      //     Name: "Inventory Shrinkage",
      //     SubAccount: false,
      //     FullyQualifiedName: "Inventory Shrinkage",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "SuppliesMaterialsCogs",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "265",
      //     Name: "Kangaroo Setup Fee",
      //     SubAccount: false,
      //     FullyQualifiedName: "Kangaroo Setup Fee",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "OfficeGeneralAdministrativeExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "212",
      //     Name: "Leave Encashment",
      //     SubAccount: false,
      //     FullyQualifiedName: "Leave Encashment",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 26597,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "52",
      //     Name: "Legal and professional fees",
      //     SubAccount: false,
      //     FullyQualifiedName: "Legal and professional fees",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "LegalProfessionalFees",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 4035.95,
      //     credit: 0,
      //     include: true,
      //     total: 2189.71,
      //     monthlySeries: [{ month: "May 2025", value: 2189.71 }],
      //     avg_monthly: 2189.71,
      //   },
      //   {
      //     Id: "20",
      //     Name: "Liabilities related to assets held for sale",
      //     SubAccount: false,
      //     FullyQualifiedName: "Liabilities related to assets held for sale",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Long Term Liability",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "10",
      //     Name: "Loan from Related Parties - Saad Walid Saad Salman",
      //     SubAccount: false,
      //     FullyQualifiedName:
      //       "Loan from Related Parties - Saad Walid Saad Salman",
      //     Active: true,
      //     Classification: "Liability",
      //     AccountType: "Long Term Liability",
      //     AccountSubType: "OtherLongTermLiabilities",
      //     CurrentBalance: -2648695.01,
      //     CurrentBalanceWithSubAccounts: -2648695.01,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 2648695.01,
      //     include: true,
      //   },
      //   {
      //     Id: "222",
      //     Name: "Loan from Related Parties - Salem R Al Noaimi",
      //     SubAccount: false,
      //     FullyQualifiedName: "Loan from Related Parties - Salem R Al Noaimi",
      //     Active: true,
      //     Classification: "Liability",
      //     AccountType: "Long Term Liability",
      //     AccountSubType: "OtherLongTermLiabilities",
      //     CurrentBalance: -338645,
      //     CurrentBalanceWithSubAccounts: -338645,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 338645,
      //     include: true,
      //   },
      //   {
      //     Id: "198",
      //     Name: "Local Flour Consumption",
      //     SubAccount: false,
      //     FullyQualifiedName: "Local Flour Consumption",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "SuppliesMaterialsCogs",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 13985.06,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "92",
      //     Name: "Logo Design",
      //     SubAccount: false,
      //     FullyQualifiedName: "Logo Design",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "19",
      //     Name: "Long-term debt",
      //     SubAccount: false,
      //     FullyQualifiedName: "Long-term debt",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Long Term Liability",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "33",
      //     Name: "Long-Term Investments",
      //     SubAccount: false,
      //     FullyQualifiedName: "Long-Term Investments",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Other Asset",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "24",
      //     Name: "Loss on discontinued operations, net of tax",
      //     SubAccount: false,
      //     FullyQualifiedName: "Loss on discontinued operations, net of tax",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "14",
      //     Name: "Loss on disposal of assets",
      //     SubAccount: false,
      //     FullyQualifiedName: "Loss on disposal of assets",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Other Income",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "23",
      //     Name: "Management compensation",
      //     SubAccount: false,
      //     FullyQualifiedName: "Management compensation",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "240",
      //     Name: "Marketing Expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Marketing Expenses",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //     total: 31574.87,
      //     monthlySeries: [{ month: "May 2025", value: 31574.87 }],
      //     avg_monthly: 31574.87,
      //   },
      //   {
      //     Id: "274",
      //     Name: "Digital Marketing- Right Media",
      //     SubAccount: true,
      //     FullyQualifiedName:
      //       "Marketing Expenses:Digital Marketing- Right Media",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "240" },
      //     debit: 16700,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "281",
      //     Name: "Email marketing automation",
      //     SubAccount: true,
      //     FullyQualifiedName: "Marketing Expenses:Email marketing automation",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "240" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "190",
      //     Name: "Facebook Ads",
      //     SubAccount: true,
      //     FullyQualifiedName: "Marketing Expenses:Facebook Ads",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "240" },
      //     debit: 81156.95,
      //     credit: 0,
      //     include: true,
      //     total: 14186.66,
      //     monthlySeries: [{ month: "May 2025", value: 14186.66 }],
      //     avg_monthly: 14186.66,
      //   },
      //   {
      //     Id: "277",
      //     Name: "Google Ads",
      //     SubAccount: true,
      //     FullyQualifiedName: "Marketing Expenses:Google Ads",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "240" },
      //     debit: 32594.73,
      //     credit: 0,
      //     include: true,
      //     total: 5466.08,
      //     monthlySeries: [{ month: "May 2025", value: 5466.08 }],
      //     avg_monthly: 5466.08,
      //   },
      //   {
      //     Id: "296",
      //     Name: "Onfleet",
      //     SubAccount: true,
      //     FullyQualifiedName: "Marketing Expenses:Onfleet",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "240" },
      //     debit: 27244.53,
      //     credit: 0,
      //     include: true,
      //     total: 5922.13,
      //     monthlySeries: [{ month: "May 2025", value: 5922.13 }],
      //     avg_monthly: 5922.13,
      //   },
      //   {
      //     Id: "275",
      //     Name: "Photoshoot Content -Wessam WHMB",
      //     SubAccount: true,
      //     FullyQualifiedName:
      //       "Marketing Expenses:Photoshoot Content -Wessam WHMB",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "240" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "244",
      //     Name: "Retainer- Studio Foreign",
      //     SubAccount: true,
      //     FullyQualifiedName: "Marketing Expenses:Retainer- Studio Foreign",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "240" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "273",
      //     Name: "Social Media - Nouran Fawzy",
      //     SubAccount: true,
      //     FullyQualifiedName: "Marketing Expenses:Social Media - Nouran Fawzy",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "240" },
      //     debit: 19000,
      //     credit: 0,
      //     include: true,
      //     total: 6000,
      //     monthlySeries: [{ month: "May 2025", value: 6000 }],
      //     avg_monthly: 6000,
      //   },
      //   {
      //     Id: "283",
      //     Name: "Social Media Conten Creation - Reza Adarle",
      //     SubAccount: true,
      //     FullyQualifiedName:
      //       "Marketing Expenses:Social Media Conten Creation - Reza Adarle",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "240" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "193",
      //     Name: "Marketing Hub Starter",
      //     SubAccount: false,
      //     FullyQualifiedName: "Marketing Hub Starter",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "32",
      //     Name: "Materials - COS",
      //     SubAccount: false,
      //     FullyQualifiedName: "Materials - COS",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "53",
      //     Name: "Meals and entertainment",
      //     SubAccount: false,
      //     FullyQualifiedName: "Meals and entertainment",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "EntertainmentMeals",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "169",
      //     Name: "Medical Insurance Exp",
      //     SubAccount: false,
      //     FullyQualifiedName: "Medical Insurance Exp",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "Insurance",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 11528.56,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "40",
      //     Name: "Misc Food Items",
      //     SubAccount: false,
      //     FullyQualifiedName: "Misc Food Items",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "SuppliesMaterialsCogs",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "333",
      //     Name: "Missive",
      //     SubAccount: false,
      //     FullyQualifiedName: "Missive",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 1416.75,
      //     credit: 0,
      //     include: true,
      //     total: 272.3,
      //     monthlySeries: [{ month: "May 2025", value: 272.3 }],
      //     avg_monthly: 272.3,
      //   },
      //   {
      //     Id: "204",
      //     Name: "Network International",
      //     SubAccount: false,
      //     FullyQualifiedName: "Network International",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Bank",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "205",
      //     Name: "Network International Charges",
      //     SubAccount: false,
      //     FullyQualifiedName: "Network International Charges",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "FinanceCosts",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "215",
      //     Name: "Non Cash to Net off",
      //     SubAccount: false,
      //     FullyQualifiedName: "Non Cash to Net off",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Bank",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "200",
      //     Name: "Non Food Items Consumption",
      //     SubAccount: false,
      //     FullyQualifiedName: "Non Food Items Consumption",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "SuppliesMaterialsCogs",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 22339.86,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "174",
      //     Name: "Noqodi Charges",
      //     SubAccount: false,
      //     FullyQualifiedName: "Noqodi Charges",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Other Expense",
      //     AccountSubType: "OtherMiscellaneousExpense",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 57,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "105",
      //     Name: "Noqodi Wallet",
      //     SubAccount: false,
      //     FullyQualifiedName: "Noqodi Wallet",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "OtherCurrentAssets",
      //     CurrentBalance: 43.44,
      //     CurrentBalanceWithSubAccounts: 43.44,
      //     ParentRef: null,
      //     debit: 43.44,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "353",
      //     Name: "Odoo Charges",
      //     SubAccount: false,
      //     FullyQualifiedName: "Odoo Charges",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "DuesSubscriptions",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "54",
      //     Name: "Office expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Office expenses",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "OfficeGeneralAdministrativeExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //     total: 426,
      //     monthlySeries: [{ month: "May 2025", value: 426 }],
      //     avg_monthly: 426,
      //   },
      //   {
      //     Id: "140",
      //     Name: "Grease Trap Cleaning",
      //     SubAccount: true,
      //     FullyQualifiedName: "Office expenses:Grease Trap Cleaning",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "OfficeGeneralAdministrativeExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "54" },
      //     debit: 2784,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "145",
      //     Name: "Laundry Expenses",
      //     SubAccount: true,
      //     FullyQualifiedName: "Office expenses:Laundry Expenses",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "OfficeGeneralAdministrativeExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "54" },
      //     debit: 1890.29,
      //     credit: 0,
      //     include: true,
      //     total: 426,
      //     monthlySeries: [{ month: "May 2025", value: 426 }],
      //     avg_monthly: 426,
      //   },
      //   {
      //     Id: "170",
      //     Name: "Pest Control Exp",
      //     SubAccount: true,
      //     FullyQualifiedName: "Office expenses:Pest Control Exp",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "OfficeGeneralAdministrativeExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "54" },
      //     debit: 2231.64,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "297",
      //     Name: "Onfleet System Expense",
      //     SubAccount: false,
      //     FullyQualifiedName: "Onfleet System Expense",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "85",
      //     Name: "Opening Balance Equity",
      //     SubAccount: false,
      //     FullyQualifiedName: "Opening Balance Equity",
      //     Active: true,
      //     Classification: "Equity",
      //     AccountType: "Equity",
      //     AccountSubType: "OpeningBalanceEquity",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "41",
      //     Name: "Other - COS",
      //     SubAccount: false,
      //     FullyQualifiedName: "Other - COS",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "279",
      //     Name: "Other Charges -Spinneys",
      //     SubAccount: false,
      //     FullyQualifiedName: "Other Charges -Spinneys",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "233",
      //     Name: "Other costs of sales - COS",
      //     SubAccount: false,
      //     FullyQualifiedName: "Other costs of sales - COS",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "OtherCostsOfServiceCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "101",
      //     Name: "Other Expense",
      //     SubAccount: false,
      //     FullyQualifiedName: "Other Expense",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Other Expense",
      //     AccountSubType: "OtherMiscellaneousExpense",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 313,
      //     credit: 0,
      //     include: false,
      //     total: 58,
      //     monthlySeries: [{ month: "May 2025", value: 58 }],
      //     avg_monthly: 58,
      //   },
      //   {
      //     Id: "199",
      //     Name: "Other Food Items Consumption",
      //     SubAccount: false,
      //     FullyQualifiedName: "Other Food Items Consumption",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "SuppliesMaterialsCogs",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 160397.17,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "55",
      //     Name: "Other general and administrative expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Other general and administrative expenses",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "OfficeGeneralAdministrativeExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "16",
      //     Name: "Other operating income (expenses)",
      //     SubAccount: false,
      //     FullyQualifiedName: "Other operating income (expenses)",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Other Income",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "22",
      //     Name: "Other selling expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Other selling expenses",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //     total: 15858.17,
      //     monthlySeries: [{ month: "May 2025", value: 15858.17 }],
      //     avg_monthly: 15858.17,
      //   },
      //   {
      //     Id: "197",
      //     Name: "Abdul Ghafar",
      //     SubAccount: true,
      //     FullyQualifiedName: "Other selling expenses:Abdul Ghafar",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "22" },
      //     debit: 17800,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "177",
      //     Name: "Aehtizaz",
      //     SubAccount: true,
      //     FullyQualifiedName: "Other selling expenses:Aehtizaz",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "22" },
      //     debit: 16960,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "136",
      //     Name: "Amanat Ali",
      //     SubAccount: true,
      //     FullyQualifiedName: "Other selling expenses:Amanat Ali",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "22" },
      //     debit: 28000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "350",
      //     Name: "Aseel Jammal",
      //     SubAccount: true,
      //     FullyQualifiedName: "Other selling expenses:Aseel Jammal",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "22" },
      //     debit: 12239.46,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "311",
      //     Name: "Ashwani Kumar",
      //     SubAccount: true,
      //     FullyQualifiedName: "Other selling expenses:Ashwani Kumar",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "22" },
      //     debit: 16000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "165",
      //     Name: "Distribution Charges",
      //     SubAccount: true,
      //     FullyQualifiedName: "Other selling expenses:Distribution Charges",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "22" },
      //     debit: 78573.93,
      //     credit: 0,
      //     include: true,
      //     total: 15858.17,
      //     monthlySeries: [{ month: "May 2025", value: 15858.17 }],
      //     avg_monthly: 15858.17,
      //   },
      //   {
      //     Id: "308",
      //     Name: "Hala Zeidan Nasser",
      //     SubAccount: true,
      //     FullyQualifiedName: "Other selling expenses:Hala Zeidan Nasser",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "22" },
      //     debit: 39245.25,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "348",
      //     Name: "Khursheed Ahmed",
      //     SubAccount: true,
      //     FullyQualifiedName: "Other selling expenses:Khursheed Ahmed",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "22" },
      //     debit: 9571.43,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "282",
      //     Name: "Muhammad Sufyan",
      //     SubAccount: true,
      //     FullyQualifiedName: "Other selling expenses:Muhammad Sufyan",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "22" },
      //     debit: 16000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "336",
      //     Name: "Naveed Ul Hassan",
      //     SubAccount: true,
      //     FullyQualifiedName: "Other selling expenses:Naveed Ul Hassan",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "22" },
      //     debit: 16000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "338",
      //     Name: "Ravi Baddula",
      //     SubAccount: true,
      //     FullyQualifiedName: "Other selling expenses:Ravi Baddula",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "22" },
      //     debit: 16000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "301",
      //     Name: "Umar Bilal",
      //     SubAccount: true,
      //     FullyQualifiedName: "Other selling expenses:Umar Bilal",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "22" },
      //     debit: 16000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "231",
      //     Name: "Zameer",
      //     SubAccount: true,
      //     FullyQualifiedName: "Other selling expenses:Zameer",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "22" },
      //     debit: 17000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "44",
      //     Name: "Other Types of Expenses-Advertising Expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Other Types of Expenses-Advertising Expenses",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "328",
      //     Name: "Owners' Current Account - Saad Walid Saad Salman",
      //     SubAccount: false,
      //     FullyQualifiedName:
      //       "Owners' Current Account - Saad Walid Saad Salman",
      //     Active: true,
      //     Classification: "Equity",
      //     AccountType: "Equity",
      //     AccountSubType: "PartnersEquity",
      //     CurrentBalance: -869505.64,
      //     CurrentBalanceWithSubAccounts: -869505.64,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 869505.64,
      //     include: true,
      //   },
      //   {
      //     Id: "327",
      //     Name: "Owners' Current Account - Salem R Al Noaimi",
      //     SubAccount: false,
      //     FullyQualifiedName: "Owners' Current Account - Salem R Al Noaimi",
      //     Active: true,
      //     Classification: "Equity",
      //     AccountType: "Equity",
      //     AccountSubType: "PartnersEquity",
      //     CurrentBalance: -705859.36,
      //     CurrentBalanceWithSubAccounts: -705859.36,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 705859.36,
      //     include: true,
      //   },
      //   {
      //     Id: "103",
      //     Name: "P.O Box Fee",
      //     SubAccount: false,
      //     FullyQualifiedName: "P.O Box Fee",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "DuesSubscriptions",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 331.68,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "194",
      //     Name: "Packing Material Consumption",
      //     SubAccount: false,
      //     FullyQualifiedName: "Packing Material Consumption",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "SuppliesMaterialsCogs",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 101078.86,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "156",
      //     Name: "Parking Expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Parking Expenses",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 223.95,
      //     credit: 0,
      //     include: false,
      //     total: 28.57,
      //     monthlySeries: [{ month: "May 2025", value: 28.57 }],
      //     avg_monthly: 28.57,
      //   },
      //   {
      //     Id: "73",
      //     Name: "Payroll Clearing",
      //     SubAccount: false,
      //     FullyQualifiedName: "Payroll Clearing",
      //     Active: true,
      //     Classification: "Liability",
      //     AccountType: "Other Current Liability",
      //     AccountSubType: "PayrollClearing",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "72",
      //     Name: "Payroll liabilities",
      //     SubAccount: false,
      //     FullyQualifiedName: "Payroll liabilities",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Other Current Liability",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "305",
      //     Name: "Pemo Charges",
      //     SubAccount: false,
      //     FullyQualifiedName: "Pemo Charges",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Other Expense",
      //     AccountSubType: "OtherMiscellaneousExpense",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 1160,
      //     credit: 0,
      //     include: true,
      //     total: 232,
      //     monthlySeries: [{ month: "May 2025", value: 232 }],
      //     avg_monthly: 232,
      //   },
      //   {
      //     Id: "303",
      //     Name: "Pemo Wallet",
      //     SubAccount: false,
      //     FullyQualifiedName: "Pemo Wallet",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Bank",
      //     AccountSubType: null,
      //     CurrentBalance: 1355.8,
      //     CurrentBalanceWithSubAccounts: 1355.8,
      //     ParentRef: null,
      //     debit: 1355.8,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "254",
      //     Name: "Podium, HG & Bin Fees",
      //     SubAccount: false,
      //     FullyQualifiedName: "Podium, HG & Bin Fees",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 725.86,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "46",
      //     Name: "Prepaid expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Prepaid expenses",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 217746.83,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "354",
      //     Name: "Odoo Prepaid Expenses",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Odoo Prepaid Expenses",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 1339.46,
      //     CurrentBalanceWithSubAccounts: 1339.46,
      //     ParentRef: { value: "46" },
      //     debit: 1339.46,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "218",
      //     Name: "Prepaid AC Maintenance",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid AC Maintenance",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 9375.07,
      //     CurrentBalanceWithSubAccounts: 9375.07,
      //     ParentRef: { value: "46" },
      //     debit: 9375.07,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "289",
      //     Name: "Prepaid Air Ticket",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid Air Ticket",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "46" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "299",
      //     Name: "Prepaid Block Charges",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid Block Charges",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "46" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "129",
      //     Name: "Prepaid Business Insurance",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid Business Insurance",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 8290.21,
      //     CurrentBalanceWithSubAccounts: 8290.21,
      //     ParentRef: { value: "46" },
      //     debit: 8290.21,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "288",
      //     Name: "Prepaid Certification, Audit and Compliance Expenses",
      //     SubAccount: true,
      //     FullyQualifiedName:
      //       "Prepaid expenses:Prepaid Certification, Audit and Compliance Expenses",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 631.64,
      //     CurrentBalanceWithSubAccounts: 631.64,
      //     ParentRef: { value: "46" },
      //     debit: 631.64,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "93",
      //     Name: "Prepaid DREC",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid DREC",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 5407.09,
      //     CurrentBalanceWithSubAccounts: 5407.09,
      //     ParentRef: { value: "46" },
      //     debit: 5407.09,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "261",
      //     Name: "Prepaid Dropbox",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid Dropbox",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 895.22,
      //     CurrentBalanceWithSubAccounts: 895.22,
      //     ParentRef: { value: "46" },
      //     debit: 895.22,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "241",
      //     Name: "Prepaid Establishment Card",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid Establishment Card",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 635.44,
      //     CurrentBalanceWithSubAccounts: 635.44,
      //     ParentRef: { value: "46" },
      //     debit: 635.44,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "110",
      //     Name: "Prepaid Fire Safety AMC",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid Fire Safety AMC",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "46" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "243",
      //     Name: "Prepaid Food Permit",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid Food Permit",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "46" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "185",
      //     Name: "Prepaid Food Watch",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid Food Watch",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 168.33,
      //     CurrentBalanceWithSubAccounts: 168.33,
      //     ParentRef: { value: "46" },
      //     debit: 168.33,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "286",
      //     Name: "Prepaid Marketing Automation",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid Marketing Automation",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "46" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "114",
      //     Name: "Prepaid Medical Insurance",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid Medical Insurance",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 7538.86,
      //     CurrentBalanceWithSubAccounts: 7538.86,
      //     ParentRef: { value: "46" },
      //     debit: 7538.86,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "**********",
      //     Name: "Prepaid Odoo Subscription",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid Odoo Subscription",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 38600.38,
      //     CurrentBalanceWithSubAccounts: 38600.38,
      //     ParentRef: { value: "46" },
      //     debit: 38600.38,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "208",
      //     Name: "Prepaid OH Card",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid OH Card",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 2265.81,
      //     CurrentBalanceWithSubAccounts: 2265.81,
      //     ParentRef: { value: "46" },
      //     debit: 2265.81,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "112",
      //     Name: "Prepaid P.O Box Rent",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid P.O Box Rent",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 663.17,
      //     CurrentBalanceWithSubAccounts: 663.17,
      //     ParentRef: { value: "46" },
      //     debit: 663.17,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "139",
      //     Name: "Prepaid Pest Control",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid Pest Control",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 0.03,
      //     CurrentBalanceWithSubAccounts: 0.03,
      //     ParentRef: { value: "46" },
      //     debit: 0.03,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "245",
      //     Name: "Prepaid Quickbook",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid Quickbook",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "46" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "272",
      //     Name: "Prepaid Road Permit",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid Road Permit",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: -78.72,
      //     CurrentBalanceWithSubAccounts: -78.72,
      //     ParentRef: { value: "46" },
      //     debit: 0,
      //     credit: 78.72,
      //     include: false,
      //   },
      //   {
      //     Id: "263",
      //     Name: "Prepaid Shopify License (Loyalty program)",
      //     SubAccount: true,
      //     FullyQualifiedName:
      //       "Prepaid expenses:Prepaid Shopify License (Loyalty program)",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "46" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "260",
      //     Name: "Prepaid Social Media Marketing",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid Social Media Marketing",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "46" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "96",
      //     Name: "Prepaid Trade License",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid Trade License",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 5980.03,
      //     CurrentBalanceWithSubAccounts: 5980.03,
      //     ParentRef: { value: "46" },
      //     debit: 5980.03,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "107",
      //     Name: "Prepaid Visa Expenses",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid Visa Expenses",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 109677.79,
      //     CurrentBalanceWithSubAccounts: 109677.79,
      //     ParentRef: { value: "46" },
      //     debit: 109677.79,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "142",
      //     Name: "Prepaid Warehouse Maintenance",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid Warehouse Maintenance",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 1950,
      //     CurrentBalanceWithSubAccounts: 1950,
      //     ParentRef: { value: "46" },
      //     debit: 1950,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "111",
      //     Name: "Prepaid Warehouse Rent",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Prepaid Warehouse Rent",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 20000.03,
      //     CurrentBalanceWithSubAccounts: 20000.03,
      //     ParentRef: { value: "46" },
      //     debit: 20000.03,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "**********",
      //     Name: "Qashio Subscription",
      //     SubAccount: true,
      //     FullyQualifiedName: "Prepaid expenses:Qashio Subscription",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "PrepaidExpenses",
      //     CurrentBalance: 4406.99,
      //     CurrentBalanceWithSubAccounts: 4406.99,
      //     ParentRef: { value: "46" },
      //     debit: 4406.99,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "47",
      //     Name: "Price Difference Nutritional Test",
      //     SubAccount: false,
      //     FullyQualifiedName: "Price Difference Nutritional Test",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "OtherCostsOfServiceCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "18",
      //     Name: "Property, plant and equipment",
      //     SubAccount: false,
      //     FullyQualifiedName: "Property, plant and equipment",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Fixed Asset",
      //     AccountSubType: "OtherFixedAssets",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 1581187.13,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "154",
      //     Name: "CCTV Cameras",
      //     SubAccount: true,
      //     FullyQualifiedName: "Property, plant and equipment:CCTV Cameras",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Fixed Asset",
      //     AccountSubType: "OtherFixedAssets",
      //     CurrentBalance: 12760,
      //     CurrentBalanceWithSubAccounts: 12760,
      //     ParentRef: { value: "18" },
      //     debit: 12760,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "278",
      //     Name: "Computer and Office Equipment",
      //     SubAccount: true,
      //     FullyQualifiedName:
      //       "Property, plant and equipment:Computer and Office Equipment",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Fixed Asset",
      //     AccountSubType: "OtherFixedAssets",
      //     CurrentBalance: 4915.78,
      //     CurrentBalanceWithSubAccounts: 4915.78,
      //     ParentRef: { value: "18" },
      //     debit: 4915.78,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "104",
      //     Name: "Fit Out",
      //     SubAccount: true,
      //     FullyQualifiedName: "Property, plant and equipment:Fit Out",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Fixed Asset",
      //     AccountSubType: "Buildings",
      //     CurrentBalance: 536050.13,
      //     CurrentBalanceWithSubAccounts: 536050.13,
      //     ParentRef: { value: "18" },
      //     debit: 536050.13,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "97",
      //     Name: "Kitchen Equipments",
      //     SubAccount: true,
      //     FullyQualifiedName:
      //       "Property, plant and equipment:Kitchen Equipments",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Fixed Asset",
      //     AccountSubType: "MachineryAndEquipment",
      //     CurrentBalance: 1023461.22,
      //     CurrentBalanceWithSubAccounts: 1023461.22,
      //     ParentRef: { value: "18" },
      //     debit: 1023461.22,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "341",
      //     Name: "Office Equipments",
      //     SubAccount: true,
      //     FullyQualifiedName: "Property, plant and equipment:Office Equipments",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Fixed Asset",
      //     AccountSubType: "OtherFixedAssets",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "18" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "130",
      //     Name: "Signage",
      //     SubAccount: true,
      //     FullyQualifiedName: "Property, plant and equipment:Signage",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Fixed Asset",
      //     AccountSubType: "OtherFixedAssets",
      //     CurrentBalance: 4000,
      //     CurrentBalanceWithSubAccounts: 4000,
      //     ParentRef: { value: "18" },
      //     debit: 4000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "318",
      //     Name: "Provision for Audit",
      //     SubAccount: false,
      //     FullyQualifiedName: "Provision for Audit",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Other Current Liability",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "325",
      //     Name: "Provision for End of Services Benifits",
      //     SubAccount: false,
      //     FullyQualifiedName: "Provision for End of Services Benifits",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Other Current Liability",
      //     AccountSubType: null,
      //     CurrentBalance: -118051,
      //     CurrentBalanceWithSubAccounts: -118051,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 118051,
      //     include: true,
      //   },
      //   {
      //     Id: "251",
      //     Name: "Purchase Achievement Fees Fixed",
      //     SubAccount: false,
      //     FullyQualifiedName: "Purchase Achievement Fees Fixed",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 394.71,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "250",
      //     Name: "Purchase Service Fee",
      //     SubAccount: false,
      //     FullyQualifiedName: "Purchase Service Fee",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 4688.1,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "81",
      //     Name: "Purchases",
      //     SubAccount: false,
      //     FullyQualifiedName: "Purchases",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "SuppliesMaterialsCogs",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "127",
      //     Name: "M Tork Paper 2 Ply",
      //     SubAccount: true,
      //     FullyQualifiedName: "Purchases:M Tork Paper 2 Ply",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "SuppliesMaterialsCogs",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "81" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "351",
      //     Name: "Qashio Transaction charges/ Subscription",
      //     SubAccount: false,
      //     FullyQualifiedName: "Qashio Transaction charges/ Subscription",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "BankCharges",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 3232.76,
      //     credit: 0,
      //     include: true,
      //     total: 1406.43,
      //     monthlySeries: [{ month: "May 2025", value: 1406.43 }],
      //     avg_monthly: 1406.43,
      //   },
      //   {
      //     Id: "**********",
      //     Name: "Qashio wallet",
      //     SubAccount: false,
      //     FullyQualifiedName: "Qashio wallet",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Bank",
      //     AccountSubType: "Checking",
      //     CurrentBalance: -3092.95,
      //     CurrentBalanceWithSubAccounts: -3092.95,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 3092.95,
      //     include: true,
      //   },
      //   {
      //     Id: "89",
      //     Name: "Quickbooks Fee",
      //     SubAccount: false,
      //     FullyQualifiedName: "Quickbooks Fee",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "LegalProfessionalFees",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 619.9,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "28",
      //     Name: "RAK BANK",
      //     SubAccount: false,
      //     FullyQualifiedName: "RAK BANK",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Bank",
      //     AccountSubType: "Checking",
      //     CurrentBalance: 1179543.17,
      //     CurrentBalanceWithSubAccounts: 1179543.17,
      //     ParentRef: null,
      //     debit: 1335790.26,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "144",
      //     Name: "Rami Badawi",
      //     SubAccount: false,
      //     FullyQualifiedName: "Rami Badawi",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Equity",
      //     AccountSubType: "PartnerContributions",
      //     CurrentBalance: -7744.14,
      //     CurrentBalanceWithSubAccounts: -7744.14,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 7744.14,
      //     include: true,
      //   },
      //   {
      //     Id: "344",
      //     Name: "Rawteen Commissions",
      //     SubAccount: false,
      //     FullyQualifiedName: "Rawteen Commissions",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 238,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "191",
      //     Name: "Recipe Content",
      //     SubAccount: false,
      //     FullyQualifiedName: "Recipe Content",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "OtherCostsOfServiceCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "280",
      //     Name: "Reconciliation Discrepancies",
      //     SubAccount: false,
      //     FullyQualifiedName: "Reconciliation Discrepancies",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Other Expense",
      //     AccountSubType: "OtherMiscellaneousExpense",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "316",
      //     Name: "Refund Amount",
      //     SubAccount: false,
      //     FullyQualifiedName: "Refund Amount",
      //     Active: true,
      //     Classification: "Liability",
      //     AccountType: "Credit Card",
      //     AccountSubType: "CreditCard",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "57",
      //     Name: "Repairs and Maintenance",
      //     SubAccount: false,
      //     FullyQualifiedName: "Repairs and Maintenance",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "RepairMaintenance",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 13652.66,
      //     credit: 0,
      //     include: true,
      //     total: 1082.8,
      //     monthlySeries: [{ month: "May 2025", value: 1082.8 }],
      //     avg_monthly: 1082.8,
      //   },
      //   {
      //     Id: "2",
      //     Name: "Retained Earnings",
      //     SubAccount: false,
      //     FullyQualifiedName: "Retained Earnings",
      //     Active: true,
      //     Classification: "Equity",
      //     AccountType: "Equity",
      //     AccountSubType: "RetainedEarnings",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 3339775.78,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "285",
      //     Name: "Retainer- Studio Foreignio",
      //     SubAccount: false,
      //     FullyQualifiedName: "Retainer- Studio Foreignio",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "15",
      //     Name: "Revenue - General",
      //     SubAccount: false,
      //     FullyQualifiedName: "Revenue - General",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Income",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "141",
      //     Name: "Saad Walid Cheques",
      //     SubAccount: false,
      //     FullyQualifiedName: "Saad Walid Cheques",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Bank",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "71",
      //     Name: "Salaries",
      //     SubAccount: false,
      //     FullyQualifiedName: "Salaries",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "PayrollExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "63",
      //     Name: "Ahsan",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries:Ahsan",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "PayrollExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "71" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "178",
      //     Name: "Jued Charmaine",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries:Jued Charmaine",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "PayrollExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "71" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "42",
      //     Name: "Rami Badawi",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries:Rami Badawi",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "PayrollExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "71" },
      //     debit: 120000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "43",
      //     Name: "Salaries COS",
      //     SubAccount: false,
      //     FullyQualifiedName: "Salaries COS",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "270",
      //     Name: "Driver",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Driver",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "43" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "269",
      //     Name: "Production Staff",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "43" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "220",
      //     Name: "Akbar",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Akbar",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 3048.39,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "115",
      //     Name: "Arun Chede",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Arun Chede",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "183",
      //     Name: "Ayub",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Ayub",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "331",
      //     Name: "Bharat",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Bharat",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 16000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "319",
      //     Name: "Bibek Nepal",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Bibek Nepal",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 16000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "**********",
      //     Name: "Chaitanya Bapurao",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Chaitanya Bapurao",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 2800,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "175",
      //     Name: "Chet",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Chet",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "120",
      //     Name: "Erwin Adrios De Luna",
      //     SubAccount: true,
      //     FullyQualifiedName:
      //       "Salaries COS:Production Staff:Erwin Adrios De Luna",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 38400,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "202",
      //     Name: "JOHN KETH",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:JOHN KETH",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "158",
      //     Name: "Kalith Mansoor",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Kalith Mansoor",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "180",
      //     Name: "Mahesh",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Mahesh",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 4030.65,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "335",
      //     Name: "Mahesh Pubudu",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Mahesh Pubudu",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 16000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "284",
      //     Name: "Mahir",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Mahir",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 14000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "184",
      //     Name: "Manny",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Manny",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "91",
      //     Name: "Marc Ryan Oro",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Marc Ryan Oro",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "203",
      //     Name: "MARK",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:MARK",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "347",
      //     Name: "Mohamed Jessel",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Mohamed Jessel",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 7285.71,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "228",
      //     Name: "Nar",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Nar",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 24750,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "314",
      //     Name: "Narvin Montillero",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Narvin Montillero",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "346",
      //     Name: "Nipun Suranjan",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Nipun Suranjan",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 9000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "227",
      //     Name: "Peter",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Peter",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 14700,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "181",
      //     Name: "Prakash",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Prakash",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 26775,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "330",
      //     Name: "Pramod",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Pramod",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 14000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "229",
      //     Name: "Praveen",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Praveen",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 12600,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "345",
      //     Name: "Rajendra Baddula",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Rajendra Baddula",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 9000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "217",
      //     Name: "Riayan",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Riayan",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 17400,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "176",
      //     Name: "Riswan",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Riswan",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "147",
      //     Name: "Rizad",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Rizad",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 26000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "310",
      //     Name: "Sudip Thapa",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Sudip Thapa",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 12000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "312",
      //     Name: "Waseeq Shareef",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Waseeq Shareef",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 14000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "230",
      //     Name: "Zanain",
      //     SubAccount: true,
      //     FullyQualifiedName: "Salaries COS:Production Staff:Zanain",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "269" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "25",
      //     Name: "Salaries Payable",
      //     SubAccount: false,
      //     FullyQualifiedName: "Salaries Payable",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Other Current Liability",
      //     AccountSubType: null,
      //     CurrentBalance: -153368.89,
      //     CurrentBalanceWithSubAccounts: -153368.89,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 153368.89,
      //     include: true,
      //   },
      //   {
      //     Id: "271",
      //     Name: "Salary Payable to Rami",
      //     SubAccount: false,
      //     FullyQualifiedName: "Salary Payable to Rami",
      //     Active: true,
      //     Classification: "Liability",
      //     AccountType: "Long Term Liability",
      //     AccountSubType: "OtherLongTermLiabilities",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "1",
      //     Name: "Sales",
      //     SubAccount: false,
      //     FullyQualifiedName: "Sales",
      //     Active: true,
      //     Classification: "Revenue",
      //     AccountType: "Income",
      //     AccountSubType: "SalesOfProductIncome",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "8",
      //     Name: "Sales - retail",
      //     SubAccount: false,
      //     FullyQualifiedName: "Sales - retail",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Income",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "9",
      //     Name: "Sales - wholesale",
      //     SubAccount: false,
      //     FullyQualifiedName: "Sales - wholesale",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Income",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "82",
      //     Name: "Sales of Product Income",
      //     SubAccount: false,
      //     FullyQualifiedName: "Sales of Product Income",
      //     Active: true,
      //     Classification: "Revenue",
      //     AccountType: "Income",
      //     AccountSubType: "SalesOfProductIncome",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 3681662.86,
      //     include: true,
      //     total: 731160.39,
      //     monthlySeries: [{ month: "May 2025", value: 731160.39 }],
      //     avg_monthly: 731160.39,
      //   },
      //   {
      //     Id: "152",
      //     Name: "Salik Expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Salik Expenses",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 9911.52,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "35",
      //     Name: "Security Deposits",
      //     SubAccount: false,
      //     FullyQualifiedName: "Security Deposits",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Asset",
      //     AccountSubType: "SecurityDeposits",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 53726,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "121",
      //     Name: "Dewa Security",
      //     SubAccount: true,
      //     FullyQualifiedName: "Security Deposits:Dewa Security",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Asset",
      //     AccountSubType: "SecurityDeposits",
      //     CurrentBalance: 2000,
      //     CurrentBalanceWithSubAccounts: 2000,
      //     ParentRef: { value: "35" },
      //     debit: 2000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "161",
      //     Name: "Dubai Municipality Security Deposit",
      //     SubAccount: true,
      //     FullyQualifiedName:
      //       "Security Deposits:Dubai Municipality Security Deposit",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Asset",
      //     AccountSubType: "SecurityDeposits",
      //     CurrentBalance: 15000,
      //     CurrentBalanceWithSubAccounts: 15000,
      //     ParentRef: { value: "35" },
      //     debit: 15000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "108",
      //     Name: "Visa's Security",
      //     SubAccount: true,
      //     FullyQualifiedName: "Security Deposits:Visa's Security",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Asset",
      //     AccountSubType: "SecurityDeposits",
      //     CurrentBalance: 18000,
      //     CurrentBalanceWithSubAccounts: 18000,
      //     ParentRef: { value: "35" },
      //     debit: 18000,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "67",
      //     Name: "Warehouse Security",
      //     SubAccount: true,
      //     FullyQualifiedName: "Security Deposits:Warehouse Security",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Asset",
      //     AccountSubType: "SecurityDeposits",
      //     CurrentBalance: 18726,
      //     CurrentBalanceWithSubAccounts: 18726,
      //     ParentRef: { value: "35" },
      //     debit: 18726,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "7",
      //     Name: "Share capital",
      //     SubAccount: false,
      //     FullyQualifiedName: "Share capital",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Equity",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: -300000,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "323",
      //     Name: "Rami Badawi",
      //     SubAccount: true,
      //     FullyQualifiedName: "Share capital:Rami Badawi",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Equity",
      //     AccountSubType: null,
      //     CurrentBalance: -3000,
      //     CurrentBalanceWithSubAccounts: -3000,
      //     ParentRef: { value: "7" },
      //     debit: 0,
      //     credit: 3000,
      //     include: true,
      //   },
      //   {
      //     Id: "322",
      //     Name: "Salem Rashed Abdulla Ali Alnuaimi",
      //     SubAccount: true,
      //     FullyQualifiedName: "Share capital:Salem Rashed Abdulla Ali Alnuaimi",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Equity",
      //     AccountSubType: null,
      //     CurrentBalance: -297000,
      //     CurrentBalanceWithSubAccounts: -297000,
      //     ParentRef: { value: "7" },
      //     debit: 0,
      //     credit: 297000,
      //     include: true,
      //   },
      //   {
      //     Id: "138",
      //     Name: "Shipping Income",
      //     SubAccount: false,
      //     FullyQualifiedName: "Shipping Income",
      //     Active: true,
      //     Classification: "Revenue",
      //     AccountType: "Income",
      //     AccountSubType: "SalesOfProductIncome",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "6",
      //     Name: "Shippment Clearing Expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Shippment Clearing Expenses",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "OtherCostsOfServiceCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 20044.29,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "157",
      //     Name: "Shopify",
      //     SubAccount: false,
      //     FullyQualifiedName: "Shopify",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 14710.47,
      //     credit: 0,
      //     include: true,
      //     total: 3350.97,
      //     monthlySeries: [{ month: "May 2025", value: 3350.97 }],
      //     avg_monthly: 3350.97,
      //   },
      //   {
      //     Id: "264",
      //     Name: "Shopify License (loyalty Program)",
      //     SubAccount: false,
      //     FullyQualifiedName: "Shopify License (loyalty Program)",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "OfficeGeneralAdministrativeExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "70",
      //     Name: "Short-term debit",
      //     SubAccount: false,
      //     FullyQualifiedName: "Short-term debit",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Other Current Liability",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "192",
      //     Name: "Social Media Handling",
      //     SubAccount: false,
      //     FullyQualifiedName: "Social Media Handling",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "255",
      //     Name: "Special Event Promo Fees",
      //     SubAccount: false,
      //     FullyQualifiedName: "Special Event Promo Fees",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 374.87,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "239",
      //     Name: "Spinneys E-Commerce",
      //     SubAccount: false,
      //     FullyQualifiedName: "Spinneys E-Commerce",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 7814.28,
      //     credit: 0,
      //     include: true,
      //     total: 1542.7,
      //     monthlySeries: [{ month: "May 2025", value: 1542.7 }],
      //     avg_monthly: 1542.7,
      //   },
      //   {
      //     Id: "234",
      //     Name: "SSIP Portal Charges",
      //     SubAccount: false,
      //     FullyQualifiedName: "SSIP Portal Charges",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 7814.28,
      //     credit: 0,
      //     include: true,
      //     total: 1542.7,
      //     monthlySeries: [{ month: "May 2025", value: 1542.7 }],
      //     avg_monthly: 1542.7,
      //   },
      //   {
      //     Id: "124",
      //     Name: "Staff Food",
      //     SubAccount: false,
      //     FullyQualifiedName: "Staff Food",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "OtherCostsOfServiceCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "196",
      //     Name: "Staff Uniform",
      //     SubAccount: false,
      //     FullyQualifiedName: "Staff Uniform",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "CostOfLabor",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 1222.41,
      //     credit: 0,
      //     include: true,
      //     total: 122.62,
      //     monthlySeries: [{ month: "May 2025", value: 122.62 }],
      //     avg_monthly: 122.62,
      //   },
      //   {
      //     Id: "58",
      //     Name: "Stationery and printing",
      //     SubAccount: false,
      //     FullyQualifiedName: "Stationery and printing",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "OfficeGeneralAdministrativeExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 5339.23,
      //     credit: 0,
      //     include: true,
      //     total: 275.66,
      //     monthlySeries: [{ month: "May 2025", value: 275.66 }],
      //     avg_monthly: 275.66,
      //   },
      //   {
      //     Id: "160",
      //     Name: "Stickers/Labels",
      //     SubAccount: false,
      //     FullyQualifiedName: "Stickers/Labels",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "SuppliesMaterialsCogs",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "123",
      //     Name: "Storage Charges",
      //     SubAccount: false,
      //     FullyQualifiedName: "Storage Charges",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "OtherCostsOfServiceCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 12395.82,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "295",
      //     Name: "Stripe",
      //     SubAccount: false,
      //     FullyQualifiedName: "Stripe",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Bank",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "294",
      //     Name: "STRIPE charges",
      //     SubAccount: false,
      //     FullyQualifiedName: "STRIPE charges",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 42764.6,
      //     credit: 0,
      //     include: true,
      //     total: 9011.23,
      //     monthlySeries: [{ month: "May 2025", value: 9011.23 }],
      //     avg_monthly: 9011.23,
      //   },
      //   {
      //     Id: "225",
      //     Name: "Supplier Audit",
      //     SubAccount: false,
      //     FullyQualifiedName: "Supplier Audit",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "LegalProfessionalFees",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "267",
      //     Name: "Supplier Audit Charg-Bakery",
      //     SubAccount: false,
      //     FullyQualifiedName: "Supplier Audit Charg-Bakery",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "OtherCostsOfServiceCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "226",
      //     Name: "Supplier Registration Expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Supplier Registration Expenses",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "LegalProfessionalFees",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "276",
      //     Name: "T Choithrams E-Commerce",
      //     SubAccount: false,
      //     FullyQualifiedName: "T Choithrams E-Commerce",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "OtherCostsOfServiceCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "148",
      //     Name: "The Pizza Guys Restaurant LLC",
      //     SubAccount: false,
      //     FullyQualifiedName: "The Pizza Guys Restaurant LLC",
      //     Active: true,
      //     Classification: "Liability",
      //     AccountType: "Accounts Payable",
      //     AccountSubType: "AccountsPayable",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "287",
      //     Name: "Tips Payable",
      //     SubAccount: false,
      //     FullyQualifiedName: "Tips Payable",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Other Current Liability",
      //     AccountSubType: null,
      //     CurrentBalance: 33310.06,
      //     CurrentBalanceWithSubAccounts: 33310.06,
      //     ParentRef: null,
      //     debit: 33310.06,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "171",
      //     Name: "Trade License Exp",
      //     SubAccount: false,
      //     FullyQualifiedName: "Trade License Exp",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "LegalProfessionalFees",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 8481.64,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "195",
      //     Name: "Traffic Fines",
      //     SubAccount: false,
      //     FullyQualifiedName: "Traffic Fines",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "LegalProfessionalFees",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "116",
      //     Name: "Traning Expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Traning Expenses",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "LegalProfessionalFees",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 490,
      //     credit: 0,
      //     include: false,
      //     total: 70,
      //     monthlySeries: [{ month: "May 2025", value: 70 }],
      //     avg_monthly: 70,
      //   },
      //   {
      //     Id: "5",
      //     Name: "Travel expenses - general and admin expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Travel expenses - general and admin expenses",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "4",
      //     Name: "Travel expenses - selling expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Travel expenses - selling expenses",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "151",
      //     Name: "Delivery Van Rent",
      //     SubAccount: true,
      //     FullyQualifiedName:
      //       "Travel expenses - selling expenses:Delivery Van Rent",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "4" },
      //     debit: 89840,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "320",
      //     Name: "Twilio",
      //     SubAccount: false,
      //     FullyQualifiedName: "Twilio",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 377.27,
      //     credit: 0,
      //     include: false,
      //     total: 75.45,
      //     monthlySeries: [{ month: "May 2025", value: 75.45 }],
      //     avg_monthly: 75.45,
      //   },
      //   {
      //     Id: "109",
      //     Name: "Unapplied Cash Bill Payment Expense",
      //     SubAccount: false,
      //     FullyQualifiedName: "Unapplied Cash Bill Payment Expense",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "UnappliedCashBillPaymentExpense",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "134",
      //     Name: "Unapplied Cash Payment Income",
      //     SubAccount: false,
      //     FullyQualifiedName: "Unapplied Cash Payment Income",
      //     Active: true,
      //     Classification: "Revenue",
      //     AccountType: "Income",
      //     AccountSubType: "UnappliedCashPaymentIncome",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "79",
      //     Name: "Uncategorised Asset",
      //     SubAccount: false,
      //     FullyQualifiedName: "Uncategorised Asset",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "OtherCurrentAssets",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "74",
      //     Name: "Uncategorised Asset ( 74 )",
      //     SubAccount: false,
      //     FullyQualifiedName: "Uncategorised Asset ( 74 )",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "OtherCurrentAssets",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "76",
      //     Name: "Uncategorised Expense",
      //     SubAccount: false,
      //     FullyQualifiedName: "Uncategorised Expense",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "OtherMiscellaneousServiceCost",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "78",
      //     Name: "Uncategorised Expense ( 78 )",
      //     SubAccount: false,
      //     FullyQualifiedName: "Uncategorised Expense ( 78 )",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "OtherMiscellaneousServiceCost",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "75",
      //     Name: "Uncategorised Income",
      //     SubAccount: false,
      //     FullyQualifiedName: "Uncategorised Income",
      //     Active: true,
      //     Classification: "Revenue",
      //     AccountType: "Income",
      //     AccountSubType: "SalesOfProductIncome",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "77",
      //     Name: "Uncategorised Income ( 77 )",
      //     SubAccount: false,
      //     FullyQualifiedName: "Uncategorised Income ( 77 )",
      //     Active: true,
      //     Classification: "Revenue",
      //     AccountType: "Income",
      //     AccountSubType: "SalesOfProductIncome",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "150",
      //     Name: "Undeposited Funds",
      //     SubAccount: false,
      //     FullyQualifiedName: "Undeposited Funds",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Other Current Asset",
      //     AccountSubType: "UndepositedFunds",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "149",
      //     Name: "United Car Rentals",
      //     SubAccount: false,
      //     FullyQualifiedName: "United Car Rentals",
      //     Active: true,
      //     Classification: "Liability",
      //     AccountType: "Accounts Payable",
      //     AccountSubType: "AccountsPayable",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "3",
      //     Name: "Unrealised loss on securities, net of tax",
      //     SubAccount: false,
      //     FullyQualifiedName: "Unrealised loss on securities, net of tax",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Other Income",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "247",
      //     Name: "Urban Food Commercial Fees",
      //     SubAccount: false,
      //     FullyQualifiedName: "Urban Food Commercial Fees",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "OtherCostsOfServiceCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "253",
      //     Name: "Advertising Fees",
      //     SubAccount: true,
      //     FullyQualifiedName: "Urban Food Commercial Fees:Advertising Fees",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "OtherCostsOfServiceCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "247" },
      //     debit: 1569.49,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "248",
      //     Name: "Govt Promotion Fee",
      //     SubAccount: true,
      //     FullyQualifiedName: "Urban Food Commercial Fees:Govt Promotion Fee",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "OtherCostsOfServiceCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "247" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "259",
      //     Name: "Opening store fee",
      //     SubAccount: true,
      //     FullyQualifiedName: "Urban Food Commercial Fees:Opening store fee",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "CostOfLaborCos",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: { value: "247" },
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "62",
      //     Name: "Utilities",
      //     SubAccount: false,
      //     FullyQualifiedName: "Utilities",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "Utilities",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "87",
      //     Name: "VAT Control",
      //     SubAccount: false,
      //     FullyQualifiedName: "VAT Control",
      //     Active: true,
      //     Classification: "Liability",
      //     AccountType: "Other Current Liability",
      //     AccountSubType: "GlobalTaxPayable",
      //     CurrentBalance: -48420.48,
      //     CurrentBalanceWithSubAccounts: -48420.48,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 48489.25,
      //     include: true,
      //   },
      //   {
      //     Id: "88",
      //     Name: "VAT Payable/(Refundable)",
      //     SubAccount: false,
      //     FullyQualifiedName: "VAT Payable/(Refundable)",
      //     Active: true,
      //     Classification: "Liability",
      //     AccountType: "Other Current Liability",
      //     AccountSubType: "GlobalTaxSuspense",
      //     CurrentBalance: -25358.27,
      //     CurrentBalanceWithSubAccounts: -25358.27,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 25358.27,
      //     include: true,
      //   },
      //   {
      //     Id: "146",
      //     Name: "Vehicle Fuel-51035",
      //     SubAccount: false,
      //     FullyQualifiedName: "Vehicle Fuel-51035",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 97531.1,
      //     credit: 0,
      //     include: true,
      //     total: 21017.01,
      //     monthlySeries: [{ month: "May 2025", value: 21017.01 }],
      //     avg_monthly: 21017.01,
      //   },
      //   {
      //     Id: "179",
      //     Name: "Vehicle Fuel-Canter",
      //     SubAccount: false,
      //     FullyQualifiedName: "Vehicle Fuel-Canter",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "159",
      //     Name: "Visa Cost",
      //     SubAccount: false,
      //     FullyQualifiedName: "Visa Cost",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "LegalProfessionalFees",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "95",
      //     Name: "Visa Expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Visa Expenses",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "LegalProfessionalFees",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 26975.65,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "13",
      //     Name: "Wage expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Wage expenses",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "PayrollExpenses",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "56",
      //     Name: "Warehouse Rent",
      //     SubAccount: false,
      //     FullyQualifiedName: "Warehouse Rent",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "RentOrLeaseOfBuildings",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 53333.32,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "304",
      //     Name: "Water Advances",
      //     SubAccount: false,
      //     FullyQualifiedName: "Water Advances",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Bank",
      //     AccountSubType: null,
      //     CurrentBalance: 5670.96,
      //     CurrentBalanceWithSubAccounts: 5670.96,
      //     ParentRef: null,
      //     debit: 5670.96,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "128",
      //     Name: "Water for Dough",
      //     SubAccount: false,
      //     FullyQualifiedName: "Water for Dough",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Cost of Goods Sold",
      //     AccountSubType: "SuppliesMaterialsCogs",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "211",
      //     Name: "Website Expenses",
      //     SubAccount: false,
      //     FullyQualifiedName: "Website Expenses",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 884.4,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "313",
      //     Name: "Wio Bank",
      //     SubAccount: false,
      //     FullyQualifiedName: "Wio Bank",
      //     Active: true,
      //     Classification: "Asset",
      //     AccountType: "Bank",
      //     AccountSubType: "Checking",
      //     CurrentBalance: 83259.08,
      //     CurrentBalanceWithSubAccounts: 83259.08,
      //     ParentRef: null,
      //     debit: 83259.08,
      //     credit: 0,
      //     include: true,
      //   },
      //   {
      //     Id: "334",
      //     Name: "Yamm.com",
      //     SubAccount: false,
      //     FullyQualifiedName: "Yamm.com",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "298",
      //     Name: "ZAPIER",
      //     SubAccount: false,
      //     FullyQualifiedName: "ZAPIER",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 1960.98,
      //     credit: 0,
      //     include: true,
      //     total: 392.2,
      //     monthlySeries: [{ month: "May 2025", value: 392.2 }],
      //     avg_monthly: 392.2,
      //   },
      //   {
      //     Id: "302",
      //     Name: "Ziina Charges",
      //     SubAccount: false,
      //     FullyQualifiedName: "Ziina Charges",
      //     Active: true,
      //     Classification: null,
      //     AccountType: "Expense",
      //     AccountSubType: null,
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 243.16,
      //     credit: 0,
      //     include: false,
      //   },
      //   {
      //     Id: "309",
      //     Name: "ZOHO Corporation",
      //     SubAccount: false,
      //     FullyQualifiedName: "ZOHO Corporation",
      //     Active: true,
      //     Classification: "Expense",
      //     AccountType: "Expense",
      //     AccountSubType: "AdvertisingPromotional",
      //     CurrentBalance: 0,
      //     CurrentBalanceWithSubAccounts: 0,
      //     ParentRef: null,
      //     debit: 0,
      //     credit: 0,
      //     include: false,
      //   },
      // ]}
    />
  );
};

export default page;
