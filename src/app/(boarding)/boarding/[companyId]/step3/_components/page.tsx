"use client";
import { useState, useMemo, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  ArrowUpRight,
  ArrowDownRight,
  TrendingUp,
  DollarSign,
  Building,
  Zap,
  Calculator,
  CheckCircle2,
  BarChart3,
  Loader,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";

import Stepper, { Step } from "@/components/ui/Stepper";
// import { api } from "@/trpc/react"; // Assuming this is for mutations like updateBoardingState
import type { FinalCustomReportI } from "@/types/finalCustomReport";

interface Step3Props {
  finalCustomReport: FinalCustomReportI[];
}

const Step3 = ({ finalCustomReport }: Step3Props) => {
  const router = useRouter();
  const params = useParams() as { companyId: string };
  const [loading, setLoading] = useState(false);

  // Use a mock for `api.quickbooks.updateBoardingState.useMutation`
  // In a real app, this would come from your tRPC client.
  const updateBoardingState = useMemo(() => {
    return {
      mutateAsync: async ({
        companyId,
        boardingStep,
      }: {
        companyId: string;
        boardingStep: number;
      }) => {
        console.log(
          `Mock: Updating boarding state for ${companyId} to step ${boardingStep}`,
        );
        // Simulate API call
        return new Promise((resolve) => setTimeout(resolve, 500));
      },
    };
  }, []);

  /**
   * Formats currency values with proper locale and currency symbol, handles K for thousands.
   */
  const formatCurrency = (amount: number, currency = "AED") => {
    // if (Math.abs(amount) >= 1000) {
    //   return `${(amount).toLocaleString("en-US", {
    //     minimumFractionDigits: 0,
    //     maximumFractionDigits: 1,
    //   })}`;
    // }
    return `${amount.toLocaleString("en-US", {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    })} ${currency}`; // Simplified for smaller numbers to match image style
  };

  return (
    <div className="flex h-full items-center justify-center">
      <Stepper
        disableStepIndicators
        initialStep={3}
        stepsNames={[
          "Connect QuickBooks",
          "Extract Data",
          "Account Review",
          "Finish Up",
        ]}
        nextButton={(step) => {
          return (
            <Button
              disabled={loading}
              className="w-full md:w-fit"
              onClick={async () => {
                setLoading(true);
                // Mock updateBoardingState
                // await updateBoardingState.mutateAsync({
                //   companyId: params.companyId,
                //   boardingStep: 4,
                // });
                router.push(`/company/${params.companyId}`);
              }}
            >
              {loading ? (
                <Loader className="animate-spin" />
              ) : (
                "Perfect! Build my cash flow forecast →"
              )}
            </Button>
          );
        }}
        backButton={(step) => {
          return (
            <Button
              variant={"outline"}
              className="w-full md:w-fit"
              onClick={() => {
                const step = Number(
                  window.location.pathname.toString().slice(-1),
                );
                router.push(`/boarding/${params.companyId}/step${step - 1}`);
              }}
            >
              Extract Data
            </Button>
          );
        }}
      >
        {/* Placeholder Steps */}
        <Step>
          <></>
        </Step>
        <Step>
          <></>
        </Step>

        <Step>
          <div className="bg-gradient-to-br from-slate-50 to-slate-100">
            {/* Header Section */}
            <div className="relative overflow-hidden bg-gradient-to-r from-slate-900 via-slate-800 to-slate-900">
              <div className="bg-grid-white/[0.02] absolute inset-0 bg-[size:60px_60px]" />
              <div className="relative mx-auto max-w-7xl px-6 py-16 sm:py-24">
                <div className="text-center">
                  <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-emerald-500/10 ring-1 ring-emerald-500/20">
                    <CheckCircle2 className="h-8 w-8 text-emerald-400" />
                  </div>
                  <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl lg:text-6xl">
                    Business Analysis
                    <span className="block text-emerald-400">Complete</span>
                  </h1>
                  <p className="mx-auto mt-6 max-w-2xl text-xl text-slate-300">
                    We've analyzed your QuickBooks data and generated
                    comprehensive insights about your business performance
                  </p>
                </div>
              </div>
            </div>

            <div className="mx-auto px-6 py-12">
              {/* Cash Position Overview */}
              <div className="mb-12">
                <div className="mb-8 flex items-center gap-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-amber-100 text-amber-600">
                    <DollarSign className="h-5 w-5" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-slate-900">
                      Cash Position Overview
                    </h2>
                    <p className="text-slate-600">
                      Your current financial standing at a glance
                    </p>
                  </div>
                </div>

                <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                  {/* Bank Accounts */}
                  <Card className="border-0 bg-white shadow-lg ring-1 ring-slate-200/50">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-slate-600">
                            Cash on Hand
                          </p>
                          <p className="text-3xl font-bold text-slate-900">
                            {formatCurrency(
                              finalCustomReport.reduce((acc, curr) => {
                                if (
                                  curr.AccountType === "Bank" &&
                                  curr.include
                                ) {
                                  acc += curr.CurrentBalance || 0;
                                }
                                return acc;
                              }, 0),
                            )}
                          </p>
                        </div>
                        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                          <Building className="h-6 w-6 text-blue-600" />
                        </div>
                      </div>
                      <div className="mt-4 flex items-center gap-2">
                        <Badge
                          variant="secondary"
                          className="bg-blue-50 text-blue-700"
                        >
                          Available
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                  {/* Accounts Receivable */}
                  <Card className="border-0 bg-white shadow-lg ring-1 ring-slate-200/50">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-slate-600">
                            Accounts Receivable
                          </p>
                          <p className="text-3xl font-bold text-emerald-600">
                            {formatCurrency(
                              finalCustomReport.find(
                                (acc) =>
                                  acc.Name == "Accounts Receivable (A/R)",
                              )?.debit || 0,
                            )}
                          </p>
                        </div>
                        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-emerald-100">
                          <ArrowUpRight className="h-6 w-6 text-emerald-600" />
                        </div>
                      </div>
                      <div className="mt-4 flex items-center gap-2">
                        <Badge
                          variant="secondary"
                          className="bg-emerald-50 text-emerald-700"
                        >
                          Incoming
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                  {/* Accounts Payable */}
                  <Card className="border-0 bg-white shadow-lg ring-1 ring-slate-200/50">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-slate-600">
                            Accounts Payable
                          </p>
                          <p className="text-3xl font-bold text-red-600">
                            {formatCurrency(
                              finalCustomReport.find(
                                (acc) => acc.Name == "Accounts Payable (A/P)",
                              )?.credit || 0,
                            )}
                          </p>
                        </div>
                        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                          <ArrowDownRight className="h-6 w-6 text-red-600" />
                        </div>
                      </div>
                      <div className="mt-4 flex items-center gap-2">
                        <Badge
                          variant="secondary"
                          className="bg-red-50 text-red-700"
                        >
                          Due
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                  {/* Annual Revenue */}
                  <Card className="border-0 bg-white shadow-lg ring-1 ring-slate-200/50 sm:col-span-2 lg:col-span-1">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-slate-600">
                            Net Earnings
                          </p>
                          <p className="text-3xl font-bold text-slate-900">
                            {formatCurrency(
                              finalCustomReport.reduce((acc, curr) => {
                                if (
                                  !curr.ParentRef &&
                                  curr.AccountType === "Income"
                                )
                                  acc += curr.total || 0;
                                return acc;
                              }, 0),
                            )}
                          </p>
                        </div>
                        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-purple-100">
                          <BarChart3 className="h-6 w-6 text-purple-600" />
                        </div>
                      </div>
                      <div className="mt-4 flex items-center gap-2">
                        <Badge
                          variant="secondary"
                          className="bg-purple-50 text-purple-700"
                        >
                          Total
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-0 bg-gradient-to-r from-emerald-500 to-emerald-600 text-white shadow-lg sm:col-span-2">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-emerald-100">
                            Net Available Cash
                          </p>
                          <p className="text-4xl font-bold">
                            {formatCurrency(
                              (finalCustomReport.find(
                                (acc) =>
                                  acc.Name == "Accounts Receivable (A/R)",
                              )?.debit || 0) -
                                (finalCustomReport.find(
                                  (acc) => acc.Name == "Accounts Payable (A/P)",
                                )?.credit || 0),
                            )}
                          </p>
                        </div>
                        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-white/20">
                          <TrendingUp className="h-6 w-6" />
                        </div>
                      </div>
                      <div className="mt-4">
                        <p className="text-sm text-emerald-100">
                          Strong liquidity position
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>

              {/* Business Performance */}
              {/* <div className="mb-12">
                <div className="mb-8 flex items-center gap-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-100 text-purple-600">
                    <BarChart3 className="h-5 w-5" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-slate-900">
                      Business Performance
                    </h2>
                    <p className="text-slate-600">
                      Key metrics and growth indicators
                    </p>
                  </div>
                </div>

                <div className="grid gap-6 md:grid-cols-3">
                  <Card className="border-0 bg-white shadow-lg ring-1 ring-slate-200/50">
                    <CardContent className="p-6">
                      <div className="text-center">
                        <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-emerald-100">
                          <TrendingUp className="h-8 w-8 text-emerald-600" />
                        </div>
                        <p className="text-3xl font-bold text-emerald-600">
                          ??K
                        </p>
                        <p className="text-sm font-medium text-slate-900">
                          Cash Generated
                        </p>
                        <p className="text-xs text-slate-500">12 months</p>
                        <div className="mt-4">
                          <Badge className="bg-emerald-50 text-emerald-700">
                            +21% cash flow margin
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-0 bg-white shadow-lg ring-1 ring-slate-200/50">
                    <CardContent className="p-6">
                      <div className="text-center">
                        <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                          <DollarSign className="h-8 w-8 text-blue-600" />
                        </div>
                        <p className="text-3xl font-bold text-blue-600">$!!K</p>
                        <p className="text-sm font-medium text-slate-900">
                          Avg Monthly Revenue
                        </p>
                        <p className="text-xs text-slate-500">
                          Growing 8% per month
                        </p>
                        <div className="mt-4">
                          <Progress value={75} className="h-2" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-0 bg-white shadow-lg ring-1 ring-slate-200/50">
                    <CardContent className="p-6">
                      <div className="text-center">
                        <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-amber-100">
                          <Zap className="h-8 w-8 text-amber-600" />
                        </div>
                        <p className="text-3xl font-bold text-amber-600">
                          Stable?
                        </p>
                        <p className="text-sm font-medium text-slate-900">
                          Revenue Pattern
                        </p>
                        <p className="text-xs text-slate-500">
                          Very predictable
                        </p>
                        <div className="mt-4">
                          <Badge className="bg-amber-50 text-amber-700">
                            Consistent
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div> */}

              {/* Income & Expenses */}
              <div className="grid gap-8 lg:grid-cols-2">
                {/* Income Sources */}
                <Card className="border-0 bg-white shadow-lg ring-1 ring-slate-200/50">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-emerald-100 text-emerald-600">
                        <ArrowUpRight className="h-4 w-4" />
                      </div>
                      <CardTitle className="text-xl text-slate-900">
                        Income Sources
                      </CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {finalCustomReport
                      .filter(
                        (acc) =>
                          acc.AccountType === "Income" &&
                          !acc.ParentRef &&
                          acc.total,
                      )
                      .sort((a, b) => {
                        return (b.total || 0) - (a.total || 0);
                      })
                      .slice(0, 5)
                      .map((acc) => (
                        <div
                          key={acc.Name}
                          className="flex items-center justify-between rounded-lg bg-slate-50 p-4"
                        >
                          <div>
                            <p className="font-semibold text-slate-900">
                              {acc.Name}
                            </p>
                            <p className="text-sm text-slate-600">
                              {formatCurrency(acc.avg_monthly || 0)}/month avg
                            </p>
                          </div>
                          <p className="text-xl font-bold text-slate-900">
                            {formatCurrency(acc.total || 0)}
                          </p>
                        </div>
                      ))}
                  </CardContent>
                </Card>

                {/* Top Expenses */}
                <Card className="border-0 bg-white shadow-lg ring-1 ring-slate-200/50">
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-red-100 text-red-600">
                        <ArrowDownRight className="h-4 w-4" />
                      </div>
                      <CardTitle className="text-xl text-slate-900">
                        Top 5 Expenses
                      </CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {finalCustomReport
                      .filter(
                        (acc) =>
                          (acc.Classification === "Expense" ||
                            acc.AccountType === "Expense") &&
                          !acc.ParentRef &&
                          acc.total,
                      )
                      .sort((a, b) => {
                        return (b.total || 0) - (a.total || 0);
                      })
                      .slice(0, 5)
                      .map((acc) => (
                        <div
                          key={acc.Name}
                          className="flex items-center justify-between rounded-lg bg-slate-50 p-4"
                        >
                          <div>
                            <p className="font-semibold text-slate-900">
                              {acc.Name}
                            </p>
                            <p className="text-sm text-slate-600">
                              {formatCurrency(acc.avg_monthly || 0)}/month avg
                            </p>
                          </div>
                          <p className="text-xl font-bold text-slate-900">
                            {formatCurrency(acc.total || 0)}
                          </p>
                        </div>
                      ))}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </Step>
        {/* Placeholder Steps */}
        <Step>
          <></>
        </Step>
      </Stepper>
    </div>
  );
};

export default Step3;
