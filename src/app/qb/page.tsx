"use client";
import { Button } from "@/components/ui/button";
import { api } from "@/trpc/react";
import React from "react";
import { DynamicTable } from "../_components/dynamic-table";

const page = () => {
  // const { data, isPending } = api.quickbooks.finalReport.useQuery({
  //   companyId: "cmb9oi5zi142tpbuttjwyv3ow",
  // });
  const { data, isPending } = api.quickbooks.tst.useQuery({
    companyId: "cmb9oi5zi142tpbuttjwyv3ow",
    // url: "ProfitAndLoss",
    // companyId: "cmav02o7g0008pbjfyny976cz",
  });

  return (
    <div className="p-10">
      {isPending ? (
        "loading..."
      ) : !data ? (
        <div className="p-3 text-2xl">get data</div>
      ) : (
        JSON.stringify(data || {})
      )}
      {/* {JSON.stringify(data || {})} */}
      {/* <div className="h-screen overflow-y-auto">
      </div> */}
      {/* <DynamicTable data={data?.filter((d) => d.totalMonthlyAvg) as any} /> */}
      {/* <Button onClick={() => mutate()}>go</Button> */}
    </div>
  );
};

export default page;
