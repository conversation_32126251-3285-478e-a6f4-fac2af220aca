"use client";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Image from "next/image";
import { api } from "@/trpc/react";
import { useEffect, useMemo, useState } from "react";
import { passwordStrength } from "check-password-strength";
import { Progress } from "@/components/ui/progress";
import { useRouter } from "next/navigation";
import { signIn } from "next-auth/react";
// import { toast } from "sonner";
import Link from "next/link";

export default function Page() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const { mutateAsync } = api.user.signUp.useMutation({
    onError: (error) => {
      // toast.error(error.message);
      setIsLoading(false);
    },
  });
  const [form, setForm] = useState({ name: "", email: "", password: "" });

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Create the user
      const user = await mutateAsync(form);

      if (user) {
        // Then sign in (this is client-side code, so it's safe to use signIn here)
        const result = await signIn("credentials", {
          email: form.email,
          password: form.password,
          redirect: false,
        });

        if (result?.error) {
          console.error("Failed to sign in after registration:", result.error);
          // toast.error("Failed to sign in after registration");
          return;
        }

        // Redirect to home page on success
        router.push("/boarding");
        // router.refresh();
      }
    } catch (error) {
      console.error("Signup failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const passStrength = useMemo(() => {
    type v = "Too weak" | "Weak" | "Medium" | "Strong";
    switch (passwordStrength(form.password).value as v) {
      case "Too weak":
        return { value: form.password.length ? 25 : 0, color: "red" };
      case "Weak":
        return { value: 50, color: "yellow" };
      case "Medium":
        return { value: 75, color: "orange" };
      case "Strong":
        return { value: 100, color: "green" };
    }
  }, [form.password]);

  return (
    <div className="bg-muted flex min-h-svh flex-col items-center justify-center p-6 md:p-10">
      <div className="w-full max-w-sm md:max-w-3xl">
        <div className={cn("flex flex-col gap-6")}>
          <Card className="overflow-hidden">
            <CardContent className="grid p-0 md:grid-cols-2">
              <form className="p-6 md:p-8" onSubmit={handleSignUp}>
                <div className="flex flex-col gap-6">
                  <div className="flex flex-col items-center text-center">
                    <h1 className="text-2xl font-bold">Create your account</h1>
                    <p className="text-muted-foreground text-balance">
                      Sign up for a Wilfredo account
                    </p>
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="name">Name</Label>
                    <Input
                      onChange={(e) => {
                        setForm({ ...form, name: e.target.value });
                      }}
                      value={form.name}
                      id="name"
                      type="text"
                      placeholder="John Doe"
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      onChange={(e) => {
                        setForm({ ...form, email: e.target.value });
                      }}
                      value={form.email}
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <div className="flex items-center">
                      <Label htmlFor="password">Password</Label>
                      {/* <a
                        href="#"
                        className="ml-auto text-sm underline-offset-2 hover:underline"
                      >
                        Forgot your password?
                      </a> */}
                    </div>
                    <Input
                      onChange={(e) => {
                        setForm({ ...form, password: e.target.value });
                      }}
                      value={form.password}
                      id="password"
                      type="password"
                      required
                    />
                    <Progress value={passStrength.value} />
                  </div>
                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? "Creating account..." : "Sign up"}
                  </Button>

                  <div className="text-center text-sm">
                    Already have an account?{" "}
                    <Link
                      href="/login"
                      className="text-primary hover:text-primary/90 underline underline-offset-4"
                    >
                      Log in
                    </Link>
                  </div>
                </div>
              </form>
              <div className="bg-muted relative hidden md:block">
                <Image
                  width={500}
                  height={500}
                  src="/wilfredoLogo.png"
                  alt="Logo"
                  className="absolute inset-0 h-full w-full object-cover dark:brightness-[0.2] dark:grayscale"
                />
              </div>
            </CardContent>
          </Card>
          <div className="text-muted-foreground hover:[&_a]:text-primary text-center text-xs text-balance [&_a]:underline [&_a]:underline-offset-4">
            By clicking continue, you agree to our{" "}
            <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a>.
          </div>
        </div>
      </div>
    </div>
  );
}
