"use client";
import { signOut } from "next-auth/react";
import {
  DropdownMenuItem,
  DropdownMenuShortcut,
} from "@/components/ui/dropdown-menu";

export const LogoutMenuItem = () => {
  return (
    <DropdownMenuItem
      onClick={async () => {
        await signOut({
          callbackUrl: "/login",
          redirect: false,
        });
        window.location.href = "/login";
      }}
    >
      Log out
      <DropdownMenuShortcut className="hidden sm:inline-flex">
        ⇧⌘Q
      </DropdownMenuShortcut>
    </DropdownMenuItem>
  );
};
