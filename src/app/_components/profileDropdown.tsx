import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

import { Settings2, UserIcon, Building2, ArrowLeftRight } from "lucide-react";

import type { User } from "@prisma/client";

import Link from "next/link";
import { LogoutMenuItem } from "./logout-menu-item";
import { db } from "@/server/db";
// import { auth } from "@/server/auth";

const ProfileDropdown = async ({
  user,
  companyId,
  companyName,
}: {
  user: Partial<User>;
  companyId?: string;
  companyName?: string;
}) => {
  // const user = auth();
  // const router = useRouter();
  const uc = await db.userCompany.findMany({
    where: { userId: user.userId },
    include: { Company: true },
  });
  // const company = await db.qbCompany.findFirst({
  //   where: { companyId },
  // });
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Avatar className="border">
          <AvatarImage
            src={
              user.image ||
              `https://api.dicebear.com/7.x/identicon/svg?seed=${user?.email || "/placeholder.svg"}`
            }
            alt="@shadcn"
          />
          <AvatarFallback>
            <Settings2 />
          </AvatarFallback>
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56">
        <DropdownMenuLabel className="flex items-center gap-2">
          <UserIcon size={20} />
          {user.name}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        {companyId && uc.length > 1 && (
          <>
            <DropdownMenuGroup>
              <DropdownMenuLabel className="text-muted-foreground flex items-center gap-2 text-xs font-medium uppercase">
                <Building2 size={16} />

                {
                  uc.find((uc) => uc.companyId === companyId)?.Company
                    ?.CompanyName
                }
              </DropdownMenuLabel>
              <Link href={"/company"}>
                <DropdownMenuItem className="pl-6">
                  <ArrowLeftRight size={16} className="mr-2" />
                  Change Company
                </DropdownMenuItem>
              </Link>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
          </>
        )}
        <Link href={`/company/${companyId}/settings`}>
          <DropdownMenuItem>Settings</DropdownMenuItem>
        </Link>
        <DropdownMenuSeparator />
        <LogoutMenuItem />
        {/* <DropdownMenuItem
          onClick={async () => {
            await signOut({
              callbackUrl: "/login",
              redirect: false,
            });
            window.location.href = "/login";
          }}
        >
          Log out
          <DropdownMenuShortcut className="hidden sm:inline-flex">
            ⇧⌘Q
          </DropdownMenuShortcut>
        </DropdownMenuItem> */}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ProfileDropdown;
