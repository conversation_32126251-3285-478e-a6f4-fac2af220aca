interface RootHeaderOption {
  Name?: string;
  Value?: string;
}

interface RootHeader {
  Time?: string;
  ReportName?: string;
  StartPeriod?: string;
  EndPeriod?: string;
  Currency?: string;
  Option?: RootHeaderOption[];
}

interface RootColumnsColumn {
  ColTitle?: string;
  ColType?: string;
}

interface RootColumns {
  Column?: RootColumnsColumn[];
}

interface RootRowsRowHeaderColData {
  value?: string;
  id?: string;
}

interface RootRowsRowHeader {
  ColData?: RootRowsRowHeaderColData[];
}

interface RootRowsRowRowsRowColData {
  value?: string;
  id?: string;
}

interface RootRowsRowRowsRow {
  type?: string;
  ColData?: RootRowsRowRowsRowColData[];
}

interface RootRowsRowRows {
  Row?: RootRowsRowRowsRow[];
}

interface RootRowsRow {
  type?: string;
  Header?: RootRowsRowHeader;
  Rows?: RootRowsRowRows;
}

interface RootRows {
  Row?: RootRowsRow[];
}

export interface TransactionListByVendorI {
  Header?: RootHeader;
  Columns?: RootColumns;
  Rows?: RootRows;
}
