interface RootHeaderOption {
  Name?: string;
  Value?: string;
}

interface RootHeader {
  Time?: string;
  ReportName?: string;
  ReportBasis?: string;
  StartPeriod?: string;
  EndPeriod?: string;
  SummarizeColumnsBy?: string;
  Currency?: string;
  Option?: RootHeaderOption[];
}

interface RootColumnsColumnMetaData {
  Name?: string;
  Value?: string;
}

interface RootColumnsColumn {
  ColTitle?: string;
  ColType?: string;
  MetaData?: RootColumnsColumnMetaData[];
}

interface RootColumns {
  Column?: RootColumnsColumn[];
}

interface RootRowsRowHeaderColData {
  value?: string;
}

interface RootRowsRowHeader {
  ColData?: RootRowsRowHeaderColData[];
}

interface RootRowsRowRowsRowHeaderColData {
  value?: string;
  id?: string;
}

interface RootRowsRowRowsRowHeader {
  ColData?: RootRowsRowRowsRowHeaderColData[];
}

interface RootRowsRowRowsRowRowsRowHeaderColData {
  value?: string;
  id?: string;
}

interface RootRowsRowRowsRowRowsRowHeader {
  ColData?: RootRowsRowRowsRowRowsRowHeaderColData[];
}

interface RootRowsRowRowsRowRowsRowRowsRowColData {
  value?: string;
  id?: string;
}

interface RootRowsRowRowsRowRowsRowRowsRow {
  type?: string;
  ColData?: RootRowsRowRowsRowRowsRowRowsRowColData[];
}

interface RootRowsRowRowsRowRowsRowRows {
  Row?: RootRowsRowRowsRowRowsRowRowsRow[];
}

interface RootRowsRowRowsRowRowsRowSummaryColData {
  value?: string;
}

interface RootRowsRowRowsRowRowsRowSummary {
  ColData?: RootRowsRowRowsRowRowsRowSummaryColData[];
}

interface RootRowsRowRowsRowRowsRowColData {
  value?: string;
  id?: string;
}

interface RootRowsRowRowsRowRowsRow {
  type?: string;
  Header?: RootRowsRowRowsRowRowsRowHeader;
  Rows?: RootRowsRowRowsRowRowsRowRows;
  Summary?: RootRowsRowRowsRowRowsRowSummary;
  ColData?: RootRowsRowRowsRowRowsRowColData[];
}

interface RootRowsRowRowsRowRows {
  Row?: RootRowsRowRowsRowRowsRow[];
}

interface RootRowsRowRowsRowSummaryColData {
  value?: string;
}

interface RootRowsRowRowsRowSummary {
  ColData?: RootRowsRowRowsRowSummaryColData[];
}

interface RootRowsRowRowsRowColData {
  value?: string;
  id?: string;
}

export interface RootRowsRowI {
  type?: string;
  Header?: RootRowsRowRowsRowHeader;
  Rows?: RootRowsRowRowsRowRows;
  Summary?: RootRowsRowRowsRowSummary;
  group?: string;

  ColData?: RootRowsRowRowsRowColData[];
}

interface RootRowsRowRows {
  Row?: RootRowsRowI[];
}

interface RootRowsRowSummaryColData {
  value?: string;
}

interface RootRowsRowSummary {
  ColData?: RootRowsRowSummaryColData[];
}

export interface RootRowsRow {
  type?: string;
  group?: string;
  Header?: RootRowsRowHeader;
  Rows?: RootRowsRowRows;
  Summary?: RootRowsRowSummary;
}

interface RootRows {
  Row?: RootRowsRow[];
}

export interface ProfitAndLossReportI {
  Header?: RootHeader;
  Columns?: RootColumns;
  Rows?: RootRows;
}
