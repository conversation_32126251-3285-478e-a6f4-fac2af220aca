interface RootSalesTaxRateListTaxRateDetailTaxRateRef {
  value?: string;
  name?: string;
}

interface RootSalesTaxRateListTaxRateDetail {
  TaxTypeApplicable?: string;
  TaxOrder?: number;
  TaxRateRef?: RootSalesTaxRateListTaxRateDetailTaxRateRef;
}

interface RootSalesTaxRateList {
  TaxRateDetail?: RootSalesTaxRateListTaxRateDetail[];
}

interface RootPurchaseTaxRateListTaxRateDetailTaxRateRef {
  value?: string;
  name?: string;
}

interface RootPurchaseTaxRateListTaxRateDetail {
  TaxTypeApplicable?: string;
  TaxOrder?: number;
  TaxRateRef?: RootPurchaseTaxRateListTaxRateDetailTaxRateRef;
}

interface RootPurchaseTaxRateList {
  TaxRateDetail?: RootPurchaseTaxRateListTaxRateDetail[];
}

interface RootMetaData {
  CreateTime?: string;
  LastUpdatedTime?: string;
}

export interface TaxCodeI {
  Name?: string;
  Description?: string;
  Active?: boolean;
  Taxable?: boolean;
  TaxGroup?: boolean;
  domain?: string;
  sparse?: boolean;
  Id?: string;
  SyncToken?: string;
  SalesTaxRateList?: RootSalesTaxRateList;
  PurchaseTaxRateList?: RootPurchaseTaxRateList;
  MetaData?: RootMetaData;
}
