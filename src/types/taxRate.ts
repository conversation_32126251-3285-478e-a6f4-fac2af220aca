interface RootAgencyRef {
  value?: string;
}

interface RootTaxReturnLineRef {
  value?: string;
}

interface RootMetaData {
  CreateTime?: string;
  LastUpdatedTime?: string;
}

interface RootEffectiveTaxRate {
  RateValue?: number;
  EffectiveDate?: string;
}

export interface TaxRateI {
  Name?: string;
  Description?: string;
  Active?: boolean;
  RateValue?: number;
  SpecialTaxType?: string;
  domain?: string;
  sparse?: boolean;
  Id?: string;
  SyncToken?: string;
  AgencyRef?: RootAgencyRef;
  TaxReturnLineRef?: RootTaxReturnLineRef;
  MetaData?: RootMetaData;
  EffectiveTaxRate?: RootEffectiveTaxRate[];
}
