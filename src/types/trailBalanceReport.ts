interface RootHeaderOption {
  Name?: string;
  Value?: string;
}

interface RootHeader {
  Time?: string;
  ReportName?: string;
  DateMacro?: string;
  ReportBasis?: string;
  StartPeriod?: string;
  EndPeriod?: string;
  SummarizeColumnsBy?: string;
  Currency?: string;
  Option?: RootHeaderOption[];
}

interface RootColumnsColumn {
  ColTitle?: string;
  ColType?: string;
}

interface RootColumns {
  Column?: RootColumnsColumn[];
}

interface RootRowsRowSummaryColData {
  value?: string;
}

interface RootRowsRowSummary {
  ColData?: RootRowsRowSummaryColData[];
}

interface RootRowsRowColData {
  value?: string;
  id?: string;
}

interface RootRowsRow {
  type?: string;
  group?: string;
  Summary?: RootRowsRowSummary;
  ColData?: RootRowsRowColData[];
}

interface RootRows {
  Row?: RootRowsRow[];
}

export interface TrialBalanceI {
  Header?: RootHeader;
  Columns?: RootColumns;
  Rows?: RootRows;
}
