interface RootSalesTermRef {
  value?: string;
}

interface RootVendorRef {
  value?: string;
}

interface RootLineItemBasedExpenseLineDetailItemRef {
  value?: string;
}

interface RootLineItemBasedExpenseLineDetailTaxCodeRef {
  value?: string;
}

interface RootLineItemBasedExpenseLineDetail {
  UnitPrice?: number;
  Qty?: number;
  ItemRef?: RootLineItemBasedExpenseLineDetailItemRef;
  TaxCodeRef?: RootLineItemBasedExpenseLineDetailTaxCodeRef;
}

interface RootLine {
  DetailType?: string;
  Description?: string;
  Amount?: number;
  ItemBasedExpenseLineDetail?: RootLineItemBasedExpenseLineDetail;
}

export interface CreateBillI {
  PrivateNote?: string;
  TxnDate?: string;
  DueDate?: string;
  GlobalTaxCalculation?: string;
  SalesTermRef?: RootSalesTermRef;
  VendorRef?: RootVendorRef;
  Line?: RootLine[];
}
