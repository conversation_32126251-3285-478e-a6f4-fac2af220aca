interface RootHeaderOption {
  Name?: string;
  Value?: string;
}

interface RootHeader {
  Time?: string;
  ReportName?: string;
  StartPeriod?: string;
  EndPeriod?: string;
  Currency?: string;
  Option?: RootHeaderOption[];
}

interface RootColumnsColumn {
  ColTitle?: string;
  ColType?: string;
}

interface RootColumns {
  Column?: RootColumnsColumn[];
}

interface RootRowsRowHeaderColData {
  value?: string;
  id?: string;
}

interface RootRowsRowHeader {
  ColData?: RootRowsRowHeaderColData[];
}

interface RootRowsRowRowsRowColData {
  value?: string;
  id?: string;
}

interface RootRowsRowRowsRow {
  type?: string;
  ColData?: RootRowsRowRowsRowColData[];
}

interface RootRowsRowRows {
  Row?: RootRowsRowRowsRow[];
}

interface RootRowsRowSummaryColData {
  value?: string;
}

interface RootRowsRowSummary {
  ColData?: RootRowsRowSummaryColData[];
}

interface RootRowsRow {
  type?: string;
  Header?: RootRowsRowHeader;
  Rows?: RootRowsRowRows;
  Summary?: RootRowsRowSummary;
}

interface RootRows {
  Row?: RootRowsRow[];
}

export interface TransactionListByCustomerI {
  Header?: RootHeader;
  Columns?: RootColumns;
  Rows?: RootRows;
}
