interface RootMonthly_total {
  month?: string;
  value?: number;
}

export interface FinalCustomReportI {
  Id?: string;
  Name?: string | null;
  SubAccount?: boolean | null;
  FullyQualifiedName?: string | null;
  Active?: boolean | null;
  Classification?: string | null;
  AccountType?: string | null;
  AccountSubType?: string | null;
  CurrentBalance?: number | null;
  CurrentBalanceWithSubAccounts?: number | null;
  ParentRef: {
    value: string;
  } | null
  debit?: number | null;
  credit?: number | null;
  include?: boolean | null;
  name?: string | null;
  total?: number | null;
  monthlySeries?: RootMonthly_total[] | null;
  avg_monthly?: number | null;
}
