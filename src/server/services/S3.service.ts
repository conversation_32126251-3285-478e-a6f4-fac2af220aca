import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { env } from "@/env"; // Assuming you use t3-env

const s3 = new S3Client({
  region: env.AMAZON_AWS_REGION,
  credentials: {
    accessKeyId: env.AMAZON_AWS_ACCESS_KEY_ID,
    secretAccessKey: env.AMAZON_AWS_SECRET_ACCESS_KEY,
  },
});

const BUCKET_NAME = env.AMAZON_AWS_S3_BUCKET_NAME;

export const s3Service = {
  async uploadFile(
    key: string,
    body: Buffer | Uint8Array | Blob | string,
    contentType: string,
  ) {
    const command = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
      Body: body,
      ContentType: contentType,
      ACL: "public-read",
      
    });
    await s3.send(command);
    return { key };
  },

  async getSignedUrl(key: string, expiresInSeconds = 3600) {
    const command = new GetObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,

    });
    const url = await getSignedUrl(s3, command);
    return url;
  },
};
