import { z } from "zod";
import { TRPCError } from "@trpc/server";
import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
  qbProcedure,
} from "@/server/api/trpc";
import sse, { on } from "@/server/SSE";

const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));
export const sseRouter = createTRPCRouter({
  // Get QuickBooks authorization URL

  addTst: publicProcedure
    .input(
      z.object({
        channelId: z.string().uuid(),
        content: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const post1 = await wait(500);
      sse.emit("tst", "111111111");
      const post2 = await wait(1000);
      sse.emit("tst", "2222222");
      const post3 = await wait(1000);
      sse.emit("tst", "333333333");
      await wait(1000);
      sse.emit("tst", "4444444444");
      await wait(1000);
      sse.emit("tst", "55555555555");
      await wait(1000);
      sse.emit("tst", "************");
      await wait(1000);
      sse.emit("tst", "7777777777777");
      await wait(1000);
      sse.emit("tst", "88888888888888");
      await wait(1000);
      sse.emit("tst", "999999999999999");
      await wait(1000);
      sse.emit("tst", "101010101010101010");
      await wait(1000);
      sse.emit("tst", "11111111111111111111");
      await wait(1000);
      sse.emit("tst", "1212121212121212121212");
      await wait(1000);
      sse.emit("tst", "131313131313131313131313");
      console.log("deone");

      return { done: true };
    }),

  on: publicProcedure
    .input(
      z.object({
        channelId: z.string().cuid(),
        eventName: z.string(),
        // lastEventId is the last event id that the client has received
        // On the first call, it will be whatever was passed in the initial setup
        // If the client reconnects, it will be the last event id that the client received
        lastEventId: z.string().nullish(),
      }),
    )
    .subscription(async function* (opts) {
      // console.log(opts.input.eventName);

      for await (const [data] of on(sse, opts.input.eventName, {
        // Passing the AbortSignal from the request automatically cancels the event emitter when the request is aborted
        signal: opts.signal,
      })) {
        const post = data;
        yield post;
      }
    }),

  // We start by subscribing to the event emitter so that we don't miss any new events while fetching
});
