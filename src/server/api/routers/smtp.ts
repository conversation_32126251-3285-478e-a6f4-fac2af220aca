import { z } from "zod";
import nodemailer from "nodemailer";
import { createTR<PERSON><PERSON>outer, protectedProcedure } from "@/server/api/trpc";
import { env } from "@/env";

// const transporter = nodemailer.createTransport({
//   host: env.SMTP_HOST,
//   port: Number(env.SMTP_PORT),
//   secure: false,
//   logger: true,
//   auth: {
//     user: env.SMTP_USER_NAME,
//     pass: env.SMTP_PASSWORD,
//     //   type: "LOGIN",
//   },
// });
// const ee = new EventEmitter();
export const smtpRouter = createTRPCRouter({
  send: protectedProcedure
    .input(
      z.object({
        // companyId: z.string(),
        email: z.string().email(),
        romessagele: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // send email implementation
    }),
});
