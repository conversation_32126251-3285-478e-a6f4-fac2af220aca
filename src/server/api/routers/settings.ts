import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { createTRPCRouter, protectedProcedure } from "@/server/api/trpc";

import { UserCompanyRole } from "@prisma/client";

// const ee = new EventEmitter();
export const settingsRouter = createTRPCRouter({
  people: protectedProcedure
    .input(z.object({ companyId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { companyId } = input;

      // Get all invitations for the company
      const invitations = await ctx.db.invitation.findMany({
        where: {
          companyId,
          active: true,
        },
        select: {
          email: true,
          role: true,
          createdAt: true,
        },
      });

      // Get all users who have joined the company
      const userCompanies = await ctx.db.userCompany.findMany({
        where: {
          companyId,
        },
        include: {
          User: {
            select: {
              email: true,
            },
          },
        },
      });

      // Create a map of accepted users by email
      const acceptedUsersMap = new Map(
        userCompanies.map((uc) => [uc.User.email, uc]),
      );

      // Define the return type for people
      type PersonData = {
        email: string;
        role: UserCompanyRole;
        status: "accepted" | "pending";
        joinedAt: Date | null;
        invitedAt: Date | null;
      };

      // Combine invitations and accepted users
      const people: PersonData[] = invitations.map((invitation) => {
        const acceptedUser = acceptedUsersMap.get(invitation.email);

        return {
          email: invitation.email,
          // Use UserCompany role if user has joined, otherwise use invitation role
          role: acceptedUser ? acceptedUser.role : invitation.role,
          status: acceptedUser ? "accepted" : "pending",
          joinedAt: acceptedUser?.createdAt ?? null,
          invitedAt: invitation.createdAt,
        };
      });

      // Add any users who might be in the company but don't have active invitations
      // (edge case handling)
      userCompanies.forEach((uc) => {
        const hasInvitation = invitations.some(
          (inv) => inv.email === uc.User.email,
        );
        if (!hasInvitation) {
          people.push({
            email: uc.User.email,
            role: uc.role,
            status: "accepted",
            joinedAt: uc.createdAt,
            invitedAt: null,
          });
        }
      });

      return people;
    }),

  invitePerson: protectedProcedure
    .input(
      z.object({
        companyId: z.string(),
        email: z.string().email(),
        role: z.enum(["admin", "editor", "member"]),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { companyId, email, role } = input;

      // Check if user already exists in the system
      const existingUser = await ctx.db.user.findUnique({
        where: { email },
      });

      // Check if there's already an active invitation for this email and company
      const existingInvitation = await ctx.db.invitation.findFirst({
        where: {
          email,
          companyId,
          active: true,
        },
      });

      if (existingInvitation) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "User is already in the company",
        });
      }

      // Check if user is already a member of the company
      if (existingUser) {
        const existingUserCompany = await ctx.db.userCompany.findFirst({
          where: {
            userId: existingUser.userId,
            companyId,
          },
        });

        if (existingUserCompany) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "User is already in the company",
          });
        }
      }

      if (existingUser) {
        // User exists in system - add to UserCompany and create invitation record
        await ctx.db.$transaction(async (tx) => {
          // Add user to company
          await tx.userCompany.create({
            data: {
              userId: existingUser.userId,
              companyId,
              role: role as any,
            },
          });

          // Create invitation record with active: true
          await tx.invitation.create({
            data: {
              email,
              companyId,
              role: role as any,
              active: true,
              userId: ctx.session!.user.userId, // invitedBy
            },
          });
        });

        return {
          success: true,
          message: "User added to company successfully",
          userExists: true,
        };
      } else {
        // User doesn't exist - create invitation only
        await ctx.db.invitation.create({
          data: {
            email,
            companyId,
            role: role as any,
            active: true,
            userId: ctx.session!.user.userId, // invitedBy
          },
        });

        return {
          success: true,
          message: "Invitation sent successfully",
          userExists: false,
        };
      }
    }),

  updatePersonRole: protectedProcedure
    .input(
      z.object({
        companyId: z.string(),
        email: z.string().email(),
        role: z.enum(["admin", "editor", "member"]),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { companyId, email, role } = input;

      // Check if the current user is the owner of the company
      const currentUserCompany = await ctx.db.userCompany.findFirst({
        where: {
          userId: ctx.session!.user.userId,
          companyId,
          role: "owner",
        },
      });

      if (!currentUserCompany) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Only the company owner can update user roles",
        });
      }

      // Check if user exists in the system
      const targetUser = await ctx.db.user.findUnique({
        where: { email },
      });

      // Check if user is a member of the company (has UserCompany record)
      const targetUserCompany = targetUser
        ? await ctx.db.userCompany.findFirst({
            where: {
              userId: targetUser.userId,
              companyId,
            },
          })
        : null;

      // Check if there's an invitation for this email
      const targetInvitation = await ctx.db.invitation.findFirst({
        where: {
          email,
          companyId,
          active: true,
        },
      });

      // User must either be a company member or have an active invitation
      if (!targetUserCompany && !targetInvitation) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User is not associated with this company",
        });
      }

      // Prevent updating owner role
      if (
        targetUserCompany?.role === "owner" ||
        targetInvitation?.role === "owner"
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Cannot update the owner's role",
        });
      }

      // Update the user's role - prioritize UserCompany if user has joined
      await ctx.db.$transaction(async (tx) => {
        if (targetUserCompany) {
          // User has joined - update UserCompany role (primary source of truth)
          await tx.userCompany.update({
            where: {
              userCompanyId: targetUserCompany.userCompanyId,
            },
            data: {
              role: role as any,
            },
          });
        } else if (targetInvitation) {
          // User hasn't joined yet - update Invitation role only
          await tx.invitation.updateMany({
            where: {
              email,
              companyId,
              active: true,
            },
            data: {
              role: role as any,
            },
          });
        }
      });

      return {
        success: true,
        message: "User role updated successfully",
      };
    }),

  deletePerson: protectedProcedure
    .input(
      z.object({
        companyId: z.string(),
        email: z.string().email(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { companyId, email } = input;

      // Check if the current user is the owner of the company
      const currentUserCompany = await ctx.db.userCompany.findFirst({
        where: {
          userId: ctx.session!.user.userId,
          companyId,
          role: "owner",
        },
      });

      if (!currentUserCompany) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Only the company owner can remove users",
        });
      }

      // Check if user exists in the system
      const targetUser = await ctx.db.user.findUnique({
        where: { email },
      });

      // Check if user is a member of the company (has UserCompany record)
      const targetUserCompany = targetUser
        ? await ctx.db.userCompany.findFirst({
            where: {
              userId: targetUser.userId,
              companyId,
            },
          })
        : null;

      // Check if there's an invitation for this email
      const targetInvitation = await ctx.db.invitation.findFirst({
        where: {
          email,
          companyId,
          active: true,
        },
      });

      // User must either be a company member or have an active invitation
      if (!targetUserCompany && !targetInvitation) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User is not associated with this company",
        });
      }

      // Prevent deleting owner
      if (
        targetUserCompany?.role === "owner" ||
        targetInvitation?.role === "owner"
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Cannot remove the company owner",
        });
      }

      // Delete from both UserCompany and Invitation tables
      await ctx.db.$transaction(async (tx) => {
        // Delete from UserCompany if exists
        if (targetUserCompany) {
          await tx.userCompany.delete({
            where: {
              userCompanyId: targetUserCompany.userCompanyId,
            },
          });
        }

        // Delete from Invitation table
        await tx.invitation.deleteMany({
          where: {
            email,
            companyId,
          },
        });
      });

      return {
        success: true,
        message: "User removed from company successfully",
      };
    }),
});
