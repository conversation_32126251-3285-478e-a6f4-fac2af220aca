import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "@/server/api/trpc";
import type { CompanyI } from "@/types/company";
import type { ETLJob, ETLJobLog, UserCompany } from "@prisma/client";
import { z } from "zod";

export const companyRouter = createTRPCRouter({
  getUserCompanies: protectedProcedure.query(async ({ ctx }) => {
    return await ctx.db.userCompany.findMany({
      where: { userId: ctx.session.user.userId },
      include: { Company: true },
    });
  }),

  getCompany: protectedProcedure
    .input(z.object({ companyId: z.string() }))
    .query(async ({ ctx, input }) => {
      type CompanyWithEtlJobs = CompanyI & {
        etlJobs: (ETLJob & { logs: ETLJobLog[] })[];
      };
      const company = await ctx.db.qbCompany.findUnique({
        where: { companyId: input.companyId },
        include: {
          etlJobs: {
            where: {
              trigger: "BOARDING",
            },
            include: { logs: true },
          },
        },
      });

      return company as any as CompanyWithEtlJobs | undefined;
    }),
  findCompany: protectedProcedure
    .input(z.object({ companyId: z.string() }))
    .query(async ({ ctx, input }) => {
      return await ctx.db.qbCompany.findUnique({
        where: { companyId: input.companyId },
      });
    }),

  // addEtlLog: protectedProcedure
  //   .input(
  //     z.object({
  //       companyId: z.string(),
  //     }),
  //   )
  //   .mutation(async ({ ctx, input }) => {
  //     return await ctx.db.eTLJobLog.create({
  //       data: {
  //         etlJobId: input.companyId,
  //         message: "test",
  //       },
  //     });
  //   }),
});
