import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "@/server/api/trpc";
import {
  S3Client,
  ListBucketsCommand,
  PutObjectCommand,
} from "@aws-sdk/client-s3";
import { env } from "@/env";
import { z } from "zod";
import { s3Service } from "@/server/services/S3.service";

const client = new S3Client({
  region: env.AMAZON_AWS_REGION,
  credentials: {
    accessKeyId: env.AMAZON_AWS_ACCESS_KEY_ID,
    secretAccessKey: env.AMAZON_AWS_SECRET_ACCESS_KEY,
  },
});

export const fileRouter = createTRPCRouter({
  getFileUrl: publicProcedure
    .input(z.object({ key: z.string() }))
    .mutation(async ({ input }) => {
      const url = await s3Service.getSignedUrl(input.key);
      return { url };
    }),

  upload: publicProcedure
    .input(
      z.object({
        key: z.string(),
        content: z.string(), // Base64 string or plain text
        contentType: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const buffer = Buffer.from(input.content, "base64"); // if you're sending base64
      await s3Service.uploadFile(input.key, buffer, input.contentType);
      return { success: true };
    }),
});
