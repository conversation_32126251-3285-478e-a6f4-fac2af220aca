import { z } from "zod";
import { TRPCError } from "@trpc/server";
import {
  createTRPCRouter,
  protectedProcedure,
  qbAuthMiddlewareV2,
  qbProcedure,
} from "@/server/api/trpc";
import { QuickBooksService } from "@/server/services/quickbooks";
import { db } from "@/server/db";
import OAuthClient from "intuit-oauth";
import QBClient from "qbo";
import sse from "@/server/SSE";
import type { ETLJob, ETLJobEntity, QbAccount } from "@prisma/client";

import { env } from "@/env";
// import transformFinancialReport from "tst";
import type { TrialBalanceI } from "@/types/trailBalanceReport";
import type {
  ProfitAndLossReportI,
  RootRowsRow,
  RootRowsRowI,
} from "@/types/profitAndLossReport";

// const ee = new EventEmitter();
export const quickbooksRouter = createTRPCRouter({
  getAuthUrl: protectedProcedure.mutation(async ({ ctx }) => {
    try {
      const authUrl = QuickBooksService.getAuthorizationUrl();
      return authUrl;
    } catch (error) {
      console.error("Error getting QuickBooks auth URL:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get QuickBooks authorization URL",
      });
    }
  }),

  getCompanyCurrency: protectedProcedure
    .input(z.object({ companyId: z.string() }))
    .query(async ({ ctx, input }) => {
      return (
        await ctx.db.qbInvoice.findFirst({
          where: {
            companyId: input.companyId,
            CurrencyRef: {
              not: undefined,
            },
          },
          select: {
            CurrencyRef: true,
          },
        })
      )?.CurrencyRef as { name: string; value: string };
    }),
  getAllTerms: protectedProcedure
    .input(z.object({ companyId: z.string() }))
    .query(async ({ ctx, input }) => {
      return await ctx.db.qbTerm.findMany({
        where: { companyId: input.companyId },
      });
    }),
  newCall: qbProcedure.mutation(async ({ ctx }) => {
    try {
      return await ctx.db.$queryRaw`
      WITH monthly_totals AS (
          SELECT 
            "ClassRef",
            DATE_TRUNC('month', "TxnDate"::DATE) AS month,
            SUM("TotalAmt") AS total_per_month
          FROM 
            public."QbInvoice"
          WHERE 
            "TxnDate" IS NOT NULL
            AND "TxnDate"::DATE >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '11 months'
          GROUP BY 
            "ClassRef", DATE_TRUNC('month', "TxnDate"::DATE)
        )

        SELECT 
          "ClassRef",
          AVG(total_per_month) AS avg_monthly_total
        FROM 
          monthly_totals
        GROUP BY 
          "ClassRef"
        ORDER BY 
          "ClassRef";
`;
    } catch (e) {
      console.log(e);
      return {};
    }
  }),
  revenueStreams: protectedProcedure
    .input(z.object({ companyId: z.string() }))
    .query(async ({ ctx, input }) => {
      try {
        // SECTION - query
        const revenueStreams = (await ctx.db.$queryRaw`
        WITH monthly_totals AS (
        SELECT 
          "ClassRef",
          DATE_TRUNC('month', "TxnDate"::DATE) AS month,
          SUM("TotalAmt") AS total_per_month
        FROM 
          public."QbInvoice"
        WHERE 
          "TxnDate" IS NOT NULL
          AND "TxnDate"::DATE >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '11 months'
          AND "companyId" = ${input.companyId}

        GROUP BY 
          "ClassRef", DATE_TRUNC('month', "TxnDate"::DATE)
      )
      SELECT 
        "ClassRef",
        AVG(total_per_month) AS avg_monthly_total
      FROM 
        monthly_totals
      GROUP BY 
        "ClassRef"
      ORDER BY 
        "ClassRef";`) as {
          avg_monthly_total: number;
          ClassRef?: {
            name?: string;
            value?: string;
          };
        }[];

        // SECTION - query
        const totalAvgResult = (await ctx.db.$queryRaw`
          WITH monthly_totals AS (
            SELECT 
              DATE_TRUNC('month', "TxnDate"::DATE) AS month,
              SUM("TotalAmt") AS total_per_month
            FROM 
              public."QbInvoice"
            WHERE 
              "TxnDate" IS NOT NULL
              AND "TxnDate"::DATE >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '11 months'
               AND "companyId" = ${input.companyId}
            GROUP BY 
              DATE_TRUNC('month', "TxnDate"::DATE)
          )
          SELECT 
            AVG(total_per_month) AS overall_avg_monthly_total
          FROM 
            monthly_totals;
`) as { overall_avg_monthly_total: number }[];

        // SECTION - query
        const currencyResult = (await ctx.db.$queryRaw`
  SELECT DISTINCT "CurrencyRef"
  FROM public."QbInvoice"
  WHERE "TxnDate" IS NOT NULL
  AND "TxnDate"::DATE >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '11 months'
   AND "companyId" = ${input.companyId}
  LIMIT 1;
`) as { CurrencyRef: string | null }[];

        // SECTION - query
        const monthlySales = (await ctx.db.$queryRaw`
          SELECT 
            TO_CHAR(DATE_TRUNC('month', "TxnDate"::DATE), 'YYYY-MM') AS month,
            SUM("TotalAmt") AS total_sales
          FROM 
            public."QbInvoice"
          WHERE 
            "TxnDate" IS NOT NULL
            AND "TxnDate"::DATE >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '11 months'
             AND "companyId" = ${input.companyId}
          GROUP BY 
            DATE_TRUNC('month', "TxnDate"::DATE)
          ORDER BY 
            month;
`) as { month: string; total_sales: number }[];

        return {
          revenueStreams: revenueStreams
            .filter((s) => s.avg_monthly_total)
            .sort((a, b) => b.avg_monthly_total - a.avg_monthly_total),
          totalAvg: totalAvgResult?.[0]?.overall_avg_monthly_total ?? 0,
          currency: currencyResult?.[0]?.CurrencyRef ?? null,
          monthlySales,
        } as {
          totalAvg: number;
          currency: { name: string; value: string } | null;
          monthlySales: { month: string; total_sales: number }[];
          revenueStreams: {
            avg_monthly_total: number;
            ClassRef?: {
              name?: string;
              value?: string;
            };
          }[];
        };
      } catch (e) {
        console.log("error lol", e);
        return {
          revenueStreams: [],
          totalAvg: 0,
          currency: null,
          monthlySales: [],
        } as {
          totalAvg: number;
          currency: { name: string; value: string } | null;
          monthlySales: { month: string; total_sales: number }[];
          revenueStreams: {
            avg_monthly_total: number;
            ClassRef?: {
              name?: string;
              value?: string;
            };
          }[];
        };
      }
    }),
  getAllClasses: protectedProcedure
    .input(z.object({ companyId: z.string() }))
    .query(async ({ ctx, input }) => {
      return await ctx.db.qbClass.findMany({
        where: { companyId: input.companyId },
      });
    }),
  customerBoarding: protectedProcedure
    .input(z.object({ companyId: z.string() }))
    .query(async ({ input }) => {
      // select total invoices for each customer
      return (await db.$queryRaw`
 WITH last_12_months_invoices AS (
  SELECT 
    i."CustomerRef"->>'value' AS customer_id,
    DATE_TRUNC('month', i."TxnDate"::date) AS invoice_month,
    SUM(i."TotalAmt") AS monthly_total
  FROM public."QbInvoice" i
  WHERE i."TxnDate"::date >= CURRENT_DATE - INTERVAL '12 months'
    AND i."companyId" = ${input.companyId}
  GROUP BY customer_id, invoice_month
),
monthly_avg AS (
  SELECT 
    customer_id,
    ROUND(AVG(monthly_total)::numeric, 2)::FLOAT AS totalMonthlyAvg
  FROM last_12_months_invoices
  GROUP BY customer_id
),
classless_invoice_count AS (
  SELECT 
    i."CustomerRef"->>'value' AS customer_id,
    COUNT(*)::INTEGER AS invoicesWithoutClass
  FROM public."QbInvoice" i
  WHERE i."ClassRef" IS NULL
    AND i."companyId" = ${input.companyId}
  GROUP BY customer_id
),
total_invoice_count AS (
  SELECT 
    i."CustomerRef"->>'value' AS customer_id,
    COUNT(*)::INTEGER AS totalInvoices
  FROM public."QbInvoice" i
  WHERE i."companyId" = ${input.companyId}
  GROUP BY customer_id
),
total_sales AS (
  SELECT 
    i."CustomerRef"->>'value' AS customer_id,
    SUM(i."TotalAmt") AS totalSales
  FROM public."QbInvoice" i
  WHERE i."companyId" = ${input.companyId}
  GROUP BY customer_id
),
most_used_class AS (
  SELECT customer_id, "ClassRef",
         ROW_NUMBER() OVER (PARTITION BY customer_id ORDER BY COUNT(*) DESC) AS rn
  FROM (
    SELECT 
      i."CustomerRef"->>'value' AS customer_id,
      i."ClassRef"
    FROM public."QbInvoice" i
    WHERE i."ClassRef" IS NOT NULL
      AND i."companyId" = ${input.companyId}
  ) sub
  GROUP BY customer_id, "ClassRef"
),
top_class_per_customer AS (
  SELECT customer_id, "ClassRef" AS mostUsedClass
  FROM most_used_class
  WHERE rn = 1
)
SELECT 
  c."customerId",
  c."DisplayName" AS name,
  COALESCE(m.totalMonthlyAvg, 0) AS "totalMonthlyAvg",
  COALESCE(cl.invoicesWithoutClass, 0) AS "invoicesWithoutClass",
  COALESCE(ic.totalInvoices, 0) AS "totalInvoices",
  COALESCE(ts.totalSales, 0) AS "totalSales",
  CASE 
    WHEN COALESCE(cl.invoicesWithoutClass, 0) > 0 
    THEN top_class.mostUsedClass 
    ELSE NULL 
  END AS mostUsedClass
FROM public."QbCustomer" c
LEFT JOIN monthly_avg m ON c."Id" = m.customer_id
LEFT JOIN classless_invoice_count cl ON c."Id" = cl.customer_id
LEFT JOIN total_invoice_count ic ON c."Id" = ic.customer_id
LEFT JOIN total_sales ts ON c."Id" = ts.customer_id
LEFT JOIN top_class_per_customer top_class ON c."Id" = top_class.customer_id
WHERE c."companyId" = ${input.companyId}
ORDER BY "invoicesWithoutClass" DESC;

    `) as any[];
    }),

  fixAllCustomerInvoices: protectedProcedure
    .input(
      z.object({
        customers: z.array(
          z.object({
            customerId: z.string(), // this is your internal customer ID
            class: z.object({
              name: z.string(),
              value: z.string(),
            }),
          }),
        ),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      for (const customer of input.customers) {
        if (!customer.class) continue;
        const qbCustomer = await ctx.db.qbCustomer.findUnique({
          where: { customerId: customer.customerId },
          select: { Id: true }, // adjust field name if needed
        });
        if (!qbCustomer?.Id) continue;
        await ctx.db.$queryRaw`
        UPDATE public."QbInvoice"
        SET "ClassRef" = ${customer.class}
        WHERE "CustomerRef"->>'value' = ${qbCustomer.Id}
        AND "ClassRef" IS NULL;
      `;
      }
    }),

  fixAllVendors: protectedProcedure
    .input(
      z.object({
        vendors: z.array(
          z.object({
            // companyId: z.string(),
            vendorId: z.string(),
            TermRef: z.object({
              // name: z.string(),
              value: z.string(),
            }),
          }),
        ),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      for (const vendor of input.vendors) {
        if (!vendor.TermRef) continue;
        await ctx.db.qbVendor.update({
          where: {
            vendorId: vendor.vendorId,
          },
          data: {
            TermRef: vendor.TermRef,
          },
        });
        // await ctx.db.$queryRaw`
        //   UPDATE public."QbVendor"
        //   SET "TermRef" = ${JSON.stringify(vendor.TermRef)}
        //   WHERE "vendorId" = ${"'" + vendor.vendorId + "'"}
        //     AND "TermRef" IS NULL;
        //     `;
      }
    }),

  vendorsBoarding: protectedProcedure
    .input(z.object({ companyId: z.string() }))
    .query(async ({ input }) => {
      return (await db.$queryRaw`
      -- All bills ever
      WITH all_bills AS (
        SELECT 
          "VendorRef"->>'value' AS vendorId,
          "TotalAmt"
        FROM public."QbBill"
        WHERE "companyId" = ${input.companyId}
      ),

      -- Bills from last 12 months for monthly average
      monthly_bills AS (
        SELECT 
          "VendorRef"->>'value' AS vendorId,
          DATE_TRUNC('month', "TxnDate"::date) AS month,
          SUM("TotalAmt") AS monthly_total
        FROM public."QbBill"
        WHERE "companyId" = ${input.companyId}
          AND "TxnDate"::date >= CURRENT_DATE - INTERVAL '12 months'
        GROUP BY "VendorRef"->>'value', DATE_TRUNC('month', "TxnDate"::date)
      ),

      -- Total spent per vendor
      total_spent AS (
        SELECT 
          vendorId,
          SUM("TotalAmt") AS totalSpent
        FROM all_bills
        GROUP BY vendorId
      ),

      -- Avg monthly spending per vendor
      avg_monthly_spent AS (
        SELECT 
          vendorId,
          AVG(monthly_total) AS avgMonthlyTotalSpent
        FROM monthly_bills
        GROUP BY vendorId
      ),

      -- Term fallback: most common SalesTermRef from bills
      term_preference AS (
        SELECT 
          "VendorRef"->>'value' AS vendorId,
          mode() WITHIN GROUP (ORDER BY "SalesTermRef") AS mostUsedTerm
        FROM public."QbBill"
        WHERE "companyId" = ${input.companyId} AND "SalesTermRef" IS NOT NULL
        GROUP BY "VendorRef"->>'value'
      )

      -- Final output
      SELECT
        v."vendorId",
        CASE 
          WHEN v."TermRef"->>'value' IS NOT NULL THEN v."TermRef"
          ELSE NULL
        END AS "TermRef",
        CASE 
          WHEN v."TermRef"->>'value' IS NULL THEN tp.mostUsedTerm
          ELSE NULL
        END AS "mostUsedTerm",
        COALESCE(ts.totalSpent, 0) AS "totalSpent",
        COALESCE(avg.avgMonthlyTotalSpent, 0) AS "avgMonthlyTotalSpent",
        v."Balance",
        v."DisplayName"
      FROM public."QbVendor" v
      LEFT JOIN total_spent ts ON v."Id" = ts.vendorId
      LEFT JOIN avg_monthly_spent avg ON v."Id" = avg.vendorId
      LEFT JOIN term_preference tp ON v."Id" = tp.vendorId
      WHERE v."companyId" = ${input.companyId}
      ORDER BY "totalSpent" DESC;

      `) as {
        vendorId: string;
        Balance: number;
        DisplayName: string;
        TermRef: {
          value: string;
        } | null;
        avgMonthlyTotalSpent: number;
        totalSpent: number;
        mostUsedTerm: {
          value: string;
        } | null;
      }[];
    }),

  startEtlProcess: qbProcedure
    .input(
      z.object({
        companyId: z.string(),
        refetch: z.boolean().default(false),
        trigger: z.enum(["BOARDING", "SYNC", "MANUAL", "SCHEDULED"]),
      }),
    )
    .use(qbAuthMiddlewareV2)
    .mutation(async ({ ctx, input }) => {
      // 1) Helper: only keep keys your Prisma schema supports
      function filterToAllowed<T extends object>(
        item: any,
        allowed: string[],
      ): T {
        const out: any = { companyId: input.companyId };
        for (const key of Object.keys(item)) {
          if (allowed.includes(key)) {
            out[key] = item[key];
          }
        }
        return out;
      }

      // 2) Initial status object
      const entities: Partial<
        Record<
          ETLJobEntity,
          {
            status: "IDLE" | "RUNNING" | "COMPLETED" | "FAILED";
            success: boolean;
            count: number;
            message: string;
            error?: string;
          }
        >
      > = {
        ACCOUNTS: { status: "IDLE", success: false, count: 0, message: "" },
        BILLS: { status: "IDLE", success: false, count: 0, message: "" },
        CLASSES: { status: "IDLE", success: false, count: 0, message: "" },
        VENDORS: { status: "IDLE", success: false, count: 0, message: "" },
        CUSTOMERS: { status: "IDLE", success: false, count: 0, message: "" },
        INVOICES: { status: "IDLE", success: false, count: 0, message: "" },
        TERMS: { status: "IDLE", success: false, count: 0, message: "" },
        ITEMS: { status: "IDLE", success: false, count: 0, message: "" },
        TAX_RATES: { status: "IDLE", success: false, count: 0, message: "" },
        TAX_CODES: { status: "IDLE", success: false, count: 0, message: "" },
      };

      // 3) Fetch or create ETLJob
      const company = await ctx.db.qbCompany.findUnique({
        where: { companyId: input.companyId },
        include: { etlJobs: { include: { logs: true } } },
      });
      if (!company)
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Company not found",
        });

      let etlJob = company.etlJobs.find((j) => j.trigger === input.trigger);
      if (!etlJob) {
        etlJob = await ctx.db.eTLJob.create({
          data: {
            companyId: input.companyId,
            trigger: input.trigger,
            status: "RUNNING",
          },
          include: { logs: true },
        });
      } else {
        if (etlJob.status === "COMPLETED" && !input.refetch) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Job already completed",
          });
        }
        await ctx.db.eTLJob.update({
          where: { etlJobId: etlJob.etlJobId },
          data: { status: "RUNNING" },
        });
      }

      // 4) Immediately push initial SSE status
      sse.emit("etl", entities);

      // 5) Entity‐to‐table mapping, including allowedFields
      const entityMappings: {
        type: ETLJobEntity;
        name: string;
        tableName: string;
        endpoint: string;
        allowedFields: string[];
      }[] = [
        {
          type: "ACCOUNTS",
          name: "Accounts",
          tableName: "qbAccount",
          endpoint: "Account",
          allowedFields: [
            "Id",
            "SyncToken",
            "Name",
            "FullyQualifiedName",
            "Active",
            "Classification",
            "AccountType",
            "AccountSubType",
            "CurrentBalance",
            "CurrentBalanceWithSubAccounts",
            "AcctNum",
            "CurrencyRef",
            "MetaData",
            "ParentRef",
            "Description",
            "SubAccount",
          ],
        },
        {
          type: "VENDORS",
          name: "Vendors",
          tableName: "qbVendor",
          endpoint: "Vendor",
          allowedFields: [
            "Id",
            "SyncToken",
            "DisplayName",
            "Balance",
            "Active",
            "GivenName",
            "FamilyName",
            "CompanyName",
            "AcctNum",
            "MiddleName",
            "Suffix",
            "TaxIdentifier",
            "Vendor1099",
            "PrintOnCheckName",
            "Title",
            "BillRate",
            "PrimaryPhone",
            "PrimaryEmailAddr",
            "WebAddr",
            "Mobile",
            "Fax",
            "CurrencyRef",
            "MetaData",
            "BillAddr",
            "TermRef",
            "V4IDPseudonym",
          ],
        },
        {
          type: "CUSTOMERS",
          name: "Customers",
          tableName: "qbCustomer",
          endpoint: "Customer",
          allowedFields: [
            "Id",
            "SyncToken",
            "GivenName",
            "FamilyName",
            "FullyQualifiedName",
            "CompanyName",
            "DisplayName",
            "Balance",
            "BalanceWithJobs",
            "Taxable",
            "Job",
            "BillWithParent",
            "PreferredDeliveryMethod",
            "IsProject",
            "ClientEntityId",
            "Level",
            "MiddleName",
            "BillAddr",
            "ShipAddr",
            "CurrencyRef",
            "MetaData",
            "PrimaryPhone",
            "PrimaryEmailAddr",
            "Mobile",
            "Fax",
            "WebAddr",
            "ParentRef",
            "Notes",
            "SalesTermRef",
            "PaymentMethodRef",
            "Title",
            "AlternatePhone",
          ],
        },
        {
          type: "INVOICES",
          name: "Invoices",
          tableName: "qbInvoice",
          endpoint: "Invoice",
          allowedFields: [
            "Id",
            "SyncToken",
            "DocNumber",
            "TxnDate",
            "DueDate",
            "TotalAmt",
            "Balance",
            "AllowIPNPayment",
            "AllowOnlinePayment",
            "AllowOnlineCreditCardPayment",
            "AllowOnlineACHPayment",
            "PrintStatus",
            "EmailStatus",
            "PrivateNote",
            "CurrencyRef",
            "MetaData",
            "CustomerRef",
            "CustomerMemo",
            "BillAddr",
            "ShipAddr",
            "SalesTermRef",
            "BillEmail",
            "DeliveryInfo",
            "CustomField",
            "LinkedTxn",
            "Line",
            "GlobalTaxCalculation",
            "DepartmentRef",
            "ClassRef",
            "EInvoiceStatus",
            "ShipDate",
            "ShipMethodRef",
            "ExchangeRate",
            "HomeTotalAmt",
          ],
        },
        {
          type: "BILLS",
          name: "Bills",
          tableName: "qbBill",
          endpoint: "Bill",
          allowedFields: [
            "Id",
            "SyncToken",
            "TxnDate",
            "DueDate",
            "TotalAmt",
            "Balance",
            "PrivateNote",
            "VendorAddr",
            "MetaData",
            "CurrencyRef",
            "VendorRef",
            "APAccountRef",
            "SalesTermRef",
            "Line",
            "LinkedTxn",
            "DocNumber",
            "GlobalTaxCalculation",
            "TxnTaxDetail",
            "DepartmentRef",
            "ExchangeRate",
          ],
        },

        {
          type: "TERMS",
          name: "Terms",
          tableName: "qbTerm",
          endpoint: "Term",
          allowedFields: [
            "Id",
            "SyncToken",
            "Name",
            "Active",
            "Type",
            "DueDays",
            "DiscountDays",
            "DayOfMonthDue",
            "DueNextMonthDays",
            "MetaData",
          ],
        },
        {
          type: "CLASSES",
          name: "Classes",
          tableName: "qbClass",
          endpoint: "Class",
          allowedFields: [
            "Id",
            "SyncToken",
            "Name",
            "FullyQualifiedName",
            "Active",
            "SubClass",
            "MetaData",
            "ParentRef",
          ],
        },
        {
          type: "ITEMS",
          name: "Items",
          tableName: "qbItem",
          endpoint: "Item",
          allowedFields: [
            "FullyQualifiedName",
            "domain",
            "Name",
            "SyncToken",
            "sparse",
            "Active",
            "Type",
            "Id",
            "MetaData",
          ],
        },
        {
          type: "TAX_RATES",
          name: "TaxRates",
          tableName: "qbTaxRate",
          endpoint: "TaxRate",
          allowedFields: [
            "RateValue",
            "AgencyRef",
            "domain",
            "Name",
            "SyncToken",
            "SpecialTaxType",
            "DisplayType",
            "sparse",
            "Active",
            "MetaData",
            "Id",
            "Description",
          ],
        },
        {
          type: "TAX_CODES",
          name: "TaxCodes",
          tableName: "qbTaxCode",
          endpoint: "TaxCode",
          allowedFields: [
            "Name",
            "Description",
            "Active",
            "Taxable",
            "TaxGroup",
            "SalesTaxRateList",
            "PurchaseTaxRateList",
            "domain",
            "sparse",
            "Id",
            "SyncToken",
            "MetaData",
          ],
        },
      ];

      // 6) Core processing function
      const processEntity = async (
        entityType: ETLJobEntity,
        entityName: string,
        apiEndpoint: string,
        tableName: string,
        allowedFields: string[],
      ) => {
        try {
          // mark RUNNING
          entities[entityType] = {
            ...entities[entityType]!,
            status: "RUNNING",
            message: `Processing ${entityName}…`,
          };
          sse.emit("etl", entities);

          // check prior log
          const priorLog = etlJob!.logs.find((l) => l.entity === entityType);
          if (priorLog && priorLog.status === "COMPLETED" && !input.refetch) {
            entities[entityType] = {
              status: "COMPLETED",
              success: true,
              count: priorLog.count || 0,
              message: `${entityName} already extracted`,
            };
            sse.emit("etl", entities);
            return;
          }

          // clear if refetch
          if (input.refetch) {
            entities[entityType] = {
              status: "RUNNING",
              success: false,
              count: 0,
              message: `Revalidating ${entityName}…`,
            };
            sse.emit("etl", entities);
            await (ctx.db as any)[tableName].deleteMany({
              where: { companyId: input.companyId },
            });
          }

          // paginate
          let totalCount = 0;
          let startPosition = 1;
          const maxResults = 1000;
          let hasMore = true;

          while (hasMore) {
            entities[entityType] = {
              ...entities[entityType]!,
              status: "RUNNING",
              message: `Processing ${entityName}… (${totalCount} so far)`,
            };
            sse.emit("etl", entities);

            const res = await ctx.qb.makeApiCall({
              url: `${env.QB_BASE_URL}/v3/company/${company.realmId}/query?query=select * from ${apiEndpoint} STARTPOSITION ${startPosition} MAXRESULTS ${maxResults}`,
              method: "GET",
            });
            const batchData = res.json.QueryResponse[apiEndpoint] || [];
            const batchCount = batchData.length;

            if (batchCount > 0) {
              // filter out unknown keys
              const toInsert = batchData.map((it: any) =>
                filterToAllowed(it, allowedFields),
              );
              await (ctx.db as any)[tableName].createMany({
                data: toInsert,
                skipDuplicates: true,
              });
            }

            totalCount += batchCount;
            if (batchCount < maxResults) hasMore = false;
            else startPosition += maxResults;
          }

          // upsert log
          await ctx.db.eTLJobLog.upsert({
            where: { etlJobLogId: priorLog?.etlJobLogId || "" },
            update: {
              etlJobLogId: priorLog?.etlJobLogId || "",
              error: null,
              message:
                totalCount > 0
                  ? `${totalCount} ${entityName.toLowerCase()} extracted successfully`
                  : `No ${entityName.toLowerCase()} found`,
              entity: entityType,
              count: totalCount,
              status: "COMPLETED",
            },
            create: {
              etlJobId: etlJob!.etlJobId,
              message:
                totalCount > 0
                  ? `${totalCount} ${entityName.toLowerCase()} extracted successfully`
                  : `No ${entityName.toLowerCase()} found`,
              entity: entityType,
              count: totalCount,
              status: "COMPLETED",
            },
          });

          entities[entityType] = {
            status: "COMPLETED",
            success: true,
            count: totalCount,
            message:
              totalCount > 0
                ? `${entityName} extracted successfully`
                : `No ${entityName.toLowerCase()} found`,
          };
          sse.emit("etl", entities);
        } catch (e) {
          const errMsg = e instanceof Error ? e.message : String(e);
          entities[entityType] = {
            status: "FAILED",
            success: false,
            count: 0,
            message: `${entityName} extraction failed`,
            error: errMsg,
          };
          sse.emit("etl", entities);

          const priorLog = etlJob!.logs.find((l) => l.entity === entityType);
          await ctx.db.eTLJobLog.upsert({
            where: { etlJobLogId: priorLog?.etlJobLogId || "" },
            update: {
              etlJobLogId: priorLog?.etlJobLogId || "",
              message: `${entityName} extraction failed`,
              entity: entityType,
              count: 0,
              status: "FAILED",
              error: errMsg,
            },
            create: {
              etlJobId: etlJob!.etlJobId,
              message: `${entityName} extraction failed`,
              entity: entityType,
              count: 0,
              status: "FAILED",
              error: errMsg,
            },
          });
        }
      };

      // 7) Run each mapping sequentially
      for (const m of entityMappings) {
        await processEntity(
          m.type,
          m.name,
          m.endpoint,
          m.tableName,
          m.allowedFields,
        );
      }

      // 8) Final job status
      const failed = Object.values(entities).some((e) => e.status === "FAILED");
      await ctx.db.eTLJob.update({
        where: { etlJobId: etlJob!.etlJobId },
        data: {
          status: failed ? "FAILED" : "COMPLETED",
          completedAt: new Date(),
          errorAt: failed ? new Date() : null,
        },
      });

      sse.emit("etl", { ...entities, done: true });
      return { success: !failed, entities };
    }),

  // todo: add the rest of the QB entities that should be filled in the database
  // Handle QuickBooks OAuth callback
  authenticateQuickBooks: protectedProcedure
    .input(
      z.object({
        code: z.string(),
        state: z.string(),
        realmId: z.string(),
        url: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { code, state, realmId, url } = input;
      const tokenData = await QuickBooksService.handleCallback(url);
      // console.log(tokenData);
      const userCompanies = await ctx.db.userCompany.findMany({
        where: {
          userId: ctx.session.user.userId,
        },
        include: {
          Company: true,
        },
      });
      console.log(`user have ${userCompanies.length} companies`);

      if (!userCompanies.length) {
        // get company info from quickbooks

        const QbCompany = (
          await QuickBooksService.getOauthClient().makeApiCall({
            url: `${env.QB_BASE_URL}/v3/company/${realmId}/companyinfo/${realmId}`,
            method: "GET",
          })
        ).json;

        console.log("QbCompany");
        console.log(QbCompany);
        console.log("QbCompany");

        const company = await db.qbCompany.create({
          data: {
            realmId,
            authInfo: { ...tokenData, createdAt: new Date() },
            // ...QbCompany.CompanyInfo,
            CompanyName: QbCompany?.CompanyInfo?.CompanyName,
            LegalName: QbCompany?.CompanyInfo?.LegalName,
            CompanyStartDate: QbCompany?.CompanyInfo?.CompanyStartDate,
            FiscalYearStartMonth: QbCompany?.CompanyInfo?.FiscalYearStartMonth,
            Country: QbCompany?.CompanyInfo?.Country,
            SupportedLanguages: QbCompany?.CompanyInfo?.SupportedLanguages,
            DefaultTimeZone: QbCompany?.CompanyInfo?.DefaultTimeZone,
            domain: QbCompany?.CompanyInfo?.domain,
            sparse: QbCompany?.CompanyInfo?.sparse,
            Id: QbCompany?.CompanyInfo?.Id,
            SyncToken: QbCompany?.CompanyInfo?.SyncToken,
            CompanyAddr: QbCompany?.CompanyInfo?.CompanyAddr,
            CustomerCommunicationAddr:
              QbCompany?.CompanyInfo?.CustomerCommunicationAddr,
            LegalAddr: QbCompany?.CompanyInfo?.LegalAddr,
            CustomerCommunicationEmailAddr:
              QbCompany?.CompanyInfo?.CustomerCommunicationEmailAddr,
            PrimaryPhone: QbCompany?.CompanyInfo?.PrimaryPhone,
            Email: QbCompany?.CompanyInfo?.Email,
            WebAddr: QbCompany?.CompanyInfo?.WebAddr,
            MetaData: QbCompany?.CompanyInfo?.MetaData,
            NameValue: QbCompany?.CompanyInfo?.NameValue,
            CompanyUsers: {
              create: {
                userId: ctx.session.user.userId,
              },
            },
          },
        });
      } else {
        const com = userCompanies.find((uc) => uc.Company.realmId === realmId);

        // create a new session for him and delete the old ones
        if (!com) {
          const QbCompany = (
            await QuickBooksService.getOauthClient().makeApiCall({
              url: `${env.QB_BASE_URL}/v3/company/${realmId}/companyinfo/${realmId}`,
              method: "GET",
            })
          ).json;
          const company = await db.qbCompany.create({
            data: {
              realmId,
              authInfo: { ...tokenData, createdAt: new Date() },
              CompanyName: QbCompany?.CompanyInfo?.CompanyName,
              LegalName: QbCompany?.CompanyInfo?.LegalName,
              CompanyStartDate: QbCompany?.CompanyInfo?.CompanyStartDate,
              FiscalYearStartMonth:
                QbCompany?.CompanyInfo?.FiscalYearStartMonth,
              Country: QbCompany?.CompanyInfo?.Country,
              SupportedLanguages: QbCompany?.CompanyInfo?.SupportedLanguages,
              DefaultTimeZone: QbCompany?.CompanyInfo?.DefaultTimeZone,
              domain: QbCompany?.CompanyInfo?.domain,
              sparse: QbCompany?.CompanyInfo?.sparse,
              Id: QbCompany?.CompanyInfo?.Id,
              SyncToken: QbCompany?.CompanyInfo?.SyncToken,
              CompanyAddr: QbCompany?.CompanyInfo?.CompanyAddr,
              CustomerCommunicationAddr:
                QbCompany?.CompanyInfo?.CustomerCommunicationAddr,
              LegalAddr: QbCompany?.CompanyInfo?.LegalAddr,
              CustomerCommunicationEmailAddr:
                QbCompany?.CompanyInfo?.CustomerCommunicationEmailAddr,
              PrimaryPhone: QbCompany?.CompanyInfo?.PrimaryPhone,
              Email: QbCompany?.CompanyInfo?.Email,
              WebAddr: QbCompany?.CompanyInfo?.WebAddr,
              MetaData: QbCompany?.CompanyInfo?.MetaData,
              NameValue: QbCompany?.CompanyInfo?.NameValue,
              CompanyUsers: {
                create: {
                  userId: ctx.session.user.userId,
                },
              },
            },
          });
        } else {
          const QbCompany = (
            await QuickBooksService.getOauthClient().makeApiCall({
              url: `${env.QB_BASE_URL}/v3/company/${realmId}/companyinfo/${realmId}`,
              method: "GET",
            })
          ).json;
          await db.qbCompany.update({
            where: {
              companyId: com?.companyId,
            },
            data: {
              authInfo: { ...tokenData, createdAt: new Date() },
              CompanyName: QbCompany?.CompanyInfo?.CompanyName,
              LegalName: QbCompany?.CompanyInfo?.LegalName,
              CompanyStartDate: QbCompany?.CompanyInfo?.CompanyStartDate,
              FiscalYearStartMonth:
                QbCompany?.CompanyInfo?.FiscalYearStartMonth,
              Country: QbCompany?.CompanyInfo?.Country,
              SupportedLanguages: QbCompany?.CompanyInfo?.SupportedLanguages,
              DefaultTimeZone: QbCompany?.CompanyInfo?.DefaultTimeZone,
              domain: QbCompany?.CompanyInfo?.domain,
              sparse: QbCompany?.CompanyInfo?.sparse,
              Id: QbCompany?.CompanyInfo?.Id,
              SyncToken: QbCompany?.CompanyInfo?.SyncToken,
              CompanyAddr: QbCompany?.CompanyInfo?.CompanyAddr,
              CustomerCommunicationAddr:
                QbCompany?.CompanyInfo?.CustomerCommunicationAddr,
              LegalAddr: QbCompany?.CompanyInfo?.LegalAddr,
              CustomerCommunicationEmailAddr:
                QbCompany?.CompanyInfo?.CustomerCommunicationEmailAddr,
              PrimaryPhone: QbCompany?.CompanyInfo?.PrimaryPhone,
              Email: QbCompany?.CompanyInfo?.Email,
              WebAddr: QbCompany?.CompanyInfo?.WebAddr,
              MetaData: QbCompany?.CompanyInfo?.MetaData,
              NameValue: QbCompany?.CompanyInfo?.NameValue,
            },
          });
        }
      }

      return {
        success: true,
      };
    }),

  getBoardingState: protectedProcedure
    .input(z.object({ companyId: z.string() }))

    .query(async ({ ctx, input }) => {
      return await ctx.db.userCompanyState.findFirst({
        where: {
          UserCompany: {
            companyId: input.companyId,
          },
        },
      });
    }),

  updateBoardingState: protectedProcedure
    .input(z.object({ companyId: z.string(), boardingStep: z.number() }))
    .mutation(async ({ ctx, input }) => {
      const userCompany = await ctx.db.userCompany.findFirst({
        where: {
          companyId: input.companyId,
        },
        include: {
          UserCompanyState: true,
        },
      });

      if (!userCompany) {
        throw new TRPCError({ code: "NOT_FOUND" });
      }
      // if no UserCompanyState create one
      if (!userCompany.UserCompanyState) {
        await ctx.db.userCompanyState.create({
          data: {
            userCompanyId: userCompany.userCompanyId,
            boardingStep: input.boardingStep,
          },
        });
      } else {
        await ctx.db.userCompanyState.update({
          where: {
            userCompanyStateId: userCompany.UserCompanyState.userCompanyStateId,
          },
          data: {
            boardingStep: input.boardingStep,
          },
        });
      }
    }),
  disconnectCompanies: protectedProcedure.query(async ({ ctx }) => {
    const userCompanies = await ctx.db.userCompany.findMany({
      where: { userId: ctx.session.user.userId },
      include: { Company: true },
    });

    for (const uc of userCompanies) {
      if (uc)
        try {
          // console.log(userCompanies[0].Company.authInfo);

          const oauthClient = new OAuthClient({
            clientId: env.QB_CLIENT_ID,
            clientSecret: env.QB_CLIENT_SECRET,
            environment: "production",
            // redirectUri: "http://localhost:3000/boarding",
            token: {
              ...(uc.Company.authInfo as any),
            },
            logging: false,
          });
          // await oauthClient.refresh();
          console.log(await oauthClient.getUserInfo());
        } catch (e) {
          await ctx.db.eTLJobLog.deleteMany({
            where: {
              etlJob: {
                companyId: uc.companyId,
              },
            },
          });
          await ctx.db.eTLJob.deleteMany({
            where: { companyId: uc.companyId },
          });
          await ctx.db.qbAccount.deleteMany({
            where: { companyId: uc.companyId },
          });
          await ctx.db.qbBill.deleteMany({
            where: { companyId: uc.companyId },
          });
          await ctx.db.qbClass.deleteMany({
            where: { companyId: uc.companyId },
          });
          await ctx.db.qbCustomer.deleteMany({
            where: { companyId: uc.companyId },
          });
          await ctx.db.qbInvoice.deleteMany({
            where: { companyId: uc.companyId },
          });
          await ctx.db.qbTerm.deleteMany({
            where: { companyId: uc.companyId },
          });
          await ctx.db.qbVendor.deleteMany({
            where: { companyId: uc.companyId },
          });
          await ctx.db.userCompanyState.deleteMany({
            where: { userCompanyId: uc.userCompanyId },
          });
          await ctx.db.userCompany.delete({
            where: { userCompanyId: uc.userCompanyId },
          });
          await ctx.db.qbCompany.delete({
            where: { companyId: uc.companyId },
          });
        }
    }
  }),
  // revenueStreamsReport: qbProcedure
  //   .input(z.object({ companyId: z.string() }))
  //   .use(qbAuthMiddlewareV2)
  //   .query(async ({ ctx, input }) => {
  //     try {
  //       const [res1, res2] = await Promise.all([
  //         ctx.qb.makeApiCall({
  //           url: `${env.QB_BASE_URL}/v3/company/${ctx.realm_id}/reports/ProfitAndLoss?date_macro=all&showrows=nonzero&showcols=nonzero&sort_order=descend&accounting_method=Accrual&summarize_column_by=Month`,
  //           method: "GET",
  //         }),
  //         ctx.qb.makeApiCall({
  //           url: `${env.QB_BASE_URL}/v3/company/${ctx.realm_id}/reports/ProfitAndLoss?date_macro=all&showrows=nonzero&showcols=nonzero&sort_order=descend&accounting_method=Accrual&column=klass`,
  //           method: "GET",
  //         }),
  //       ]);

  //       // return transformFinancialReport(res1.json as any, res2.json as any);
  //     } catch (e) {
  //       console.log("sad: --- ", e);
  //     }
  //   }),

  qbReport: qbProcedure
    .input(z.object({ companyId: z.string(), url: z.string() }))
    .use(qbAuthMiddlewareV2)
    .query(async ({ ctx, input }) => {
      try {
        const response = await ctx.qb.makeApiCall({
          url: `${env.QB_BASE_URL}/v3/company/${ctx.realm_id}/reports/${input.url}`,
          method: "GET",
        });
        return response.json;
      } catch (e) {
        console.log(input.url, " FAILED: ", e);
      }
    }),

  finalReport: qbProcedure
    .input(z.object({ companyId: z.string() }))
    .use(qbAuthMiddlewareV2)
    .query(async ({ ctx, input }) => {
      // Fetch all accounts from your database
      const accounts: QbAccount[] = await ctx.db.qbAccount.findMany({
        where: {
          companyId: input.companyId,
        },
      });

      // Fetch Trial Balance report
      const trailBalance = (
        await ctx.qb.makeApiCall({
          url: `${env.QB_BASE_URL}/v3/company/${ctx.realm_id}/reports/TrialBalance`,
          method: "GET",
        })
      ).json as TrialBalanceI;

      // Calculate dates for Profit and Loss report
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 12);
      const endDate = new Date();
      const startDateStr = startDate.toISOString().split("T")[0];
      const endDateStr = endDate.toISOString().split("T")[0];
      console.log(startDateStr, endDateStr);

      // Fetch Profit and Loss report
      const profitAndLoss = (
        await ctx.qb.makeApiCall({
          url: `${env.QB_BASE_URL}/v3/company/${ctx.realm_id}/reports/ProfitAndLoss?start_date=2025-04-01&end_date=2025-04-30&showrows=nonzero&showcols=nonzero&sort_order=descend&accounting_method=Accrual&summarize_column_by=Month`,
          // url: `${env.QB_BASE_URL}/v3/company/${ctx.realm_id}/reports/ProfitAndLoss?start_date=${startDateStr}&end_date=${endDateStr}&showrows=nonzero&showcols=nonzero&sort_order=descend&accounting_method=Accrual&summarize_column_by=Month`,
          method: "GET",
        })
      ).json as ProfitAndLossReportI;

      function findAccounts(
        rows: RootRowsRowI[],
        accountId: string,
      ): Record<string, any> | null {
        if (!rows) return null;
        for (const row of rows) {
          if (
            row.type === "Data" &&
            row?.ColData?.length &&
            row.ColData[0] &&
            row.ColData[0].id == accountId
          ) {
            const total = +(
              row?.ColData?.[(row?.ColData?.length || 0) - 1]?.value || "0"
            );
            const monthlySeries = row.ColData.map((c, i) => {
              return {
                month: profitAndLoss.Columns?.Column?.[i]?.ColTitle || "",
                value: +(c.value || 0),
              };
            }).slice(1, -1);
            const avg_monthly = total ? total / monthlySeries.length : 0;
            return {
              total,
              monthlySeries,
              avg_monthly,
            };
          }
          if (row.Header && row.type === "Section" && !row.group) {
            if (row?.Header?.ColData?.[0]?.id == accountId) {
              const total = +(
                row?.Summary?.ColData?.[
                  (row?.Summary?.ColData?.length || 0) - 1
                ]?.value || "0"
              );
              const monthlySeries = row?.Summary?.ColData?.map((c, i) => {
                return {
                  month: profitAndLoss.Columns?.Column?.[i]?.ColTitle || "",
                  value: +(c.value || 0),
                };
              }).slice(1, -1);
              const avg_monthly = total
                ? total / (monthlySeries?.length || 1)
                : 0;
              return {
                total,
                monthlySeries,
                avg_monthly,
              };
            }
            if (row?.Rows?.Row) {
              const found = findAccounts(row.Rows.Row, accountId);
              if (found) return found;
            }
          }
          if (row.type === "Section" && row.group && row?.Rows?.Row) {
            const found = findAccounts(row.Rows.Row, accountId);
            if (found) return found;
          }
        }
        return null;
      }
      const finalAccounts = accounts.map((account) => {
        const {
          Id,
          Name,
          SubAccount,
          FullyQualifiedName,
          Active,
          Classification,
          AccountType,
          AccountSubType,
          CurrentBalance,
          CurrentBalanceWithSubAccounts,
          ParentRef,
        } = account;

        const tbRow = trailBalance.Rows?.Row?.find(
          (r) => r?.ColData?.[0]?.id === Id,
        );
        const debit = tbRow ? +(tbRow.ColData?.[1]?.value || 0) : 0;
        const credit = tbRow ? +(tbRow.ColData?.[2]?.value || 0) : 0;
        const include =
          Math.abs(debit) > 500 ||
          Math.abs(credit) > 500 ||
          (!SubAccount && (CurrentBalanceWithSubAccounts || 0) > 100);

        const accountInfo =
          findAccounts(profitAndLoss?.Rows?.Row || [], Id || "") || {};

        const baseAccount = {
          Id,
          Name,
          SubAccount,
          FullyQualifiedName,
          Active,
          Classification,
          AccountType,
          AccountSubType,
          CurrentBalance,
          CurrentBalanceWithSubAccounts,
          ParentRef,
          debit,
          credit,
          include,
          ...accountInfo,
        };

        return baseAccount;
      });

      return finalAccounts;
    }),

  qbReportMutate: qbProcedure
    .input(z.object({ companyId: z.string(), url: z.string() }))
    .use(qbAuthMiddlewareV2)
    .mutation(async ({ ctx, input }) => {
      try {
        const response = await ctx.qb.makeApiCall({
          url: `${env.QB_BASE_URL}/v3/company/${ctx.realm_id}/reports/${input.url}`,
          method: "GET",
        });
        return response.json;
      } catch (e) {
        console.log(input.url, " FAILED: ", e);
      }
    }),

  createBillOcr: qbProcedure
    .input(
      z.object({
        companyId: z.string(),
        base64: z.string(),
      }),
    )
    .use(qbAuthMiddlewareV2)
    .mutation(async ({ ctx, input }) => {
      try {
        const bill = {
          SalesTermRef: {
            value: "6",
          },
          Line: [
            {
              DetailType: "ItemBasedExpenseLineDetail",
              Description: "MAXIPULL EMBOSSED 2PLY 1X6 PURE P650 SC",
              ItemBasedExpenseLineDetail: {
                ItemRef: {
                  value: "22",
                  name: "Maxipull 2 Ply",
                },
                UnitPrice: 38.0,
                Qty: 12.0,
                TaxCodeRef: {
                  value: "17",
                },
              },
            },
            {
              DetailType: "ItemBasedExpenseLineDetail",
              Description: "LD GARBAGE BAG RECYCLED 110X130CM BLACK 20KG",
              ItemBasedExpenseLineDetail: {
                ItemRef: {
                  value: "26",
                  name: "Garbage Bag 110x130 cm",
                },
                UnitPrice: 95.0,
                Qty: 1.0,
                TaxCodeRef: {
                  value: "17",
                },
              },
            },
          ],
          VendorRef: {
            value: "39",
            name: "Dhofar Global Trading Co. L.L.C.",
          },
          PrivateNote: "WILFREDO TEST ",
          TxnDate: "2025-06-29",
          DueDate: "2025-06-30",
        };
        const bill2 = {
          VendorRef: { value: "39" },
          Line: [
            {
              DetailType: "ItemBasedExpenseLineDetail",
              Amount: 10,
              ItemBasedExpenseLineDetail: {
                ItemRef: { value: "22" },
              },
            },
          ],
        };
        console.log({
          url: `${env.QB_BASE_URL}/v3/company/${ctx.realm_id}/bill`,
          method: "POST",
          body: JSON.stringify(bill2),
          // headers: {
          //   "content-type": "application/json",
          //   accept: "application/json",
          // },
        });

        const res1 = await ctx.qb.makeApiCall({
          url: `${env.QB_BASE_URL}/v3/company/${ctx.realm_id}/bill`,
          method: "POST",
          body: JSON.stringify(bill2),
          headers: {
            "content-type": "application/json",
            accept: "application/json",
          },
        });
        console.log(res1.processResponse({ json: true }));
        return res1.json;
      } catch (e: any) {
        const errorBody = await e?.response?.text?.();
        console.log("QB Error Response:", errorBody || e);
      }
    }),

  tst: qbProcedure
    .input(z.object({ companyId: z.string() }))
    .use(qbAuthMiddlewareV2)
    .query(async ({ ctx, input }) => {
     
      try {
   
        const res1 = await ctx.qb.makeApiCall({
          // url: `${env.QB_BASE_URL}/v3/company/${ctx.realm_id}/reports/GeneralLedger?start_date=2025-01-01&end_date=2025-05-31`,
          url: `${env.QB_BASE_URL}/v3/company/${ctx.realm_id}/query?query=select * from PaymentMethod`, //SELECT * FROM TaxCode
          // url: `${env.QB_BASE_URL}/v3/company/${ctx.realm_id}/query?query=Select * From TaxRate`, //SELECT * FROM TaxCode
          method: "GET",
        });
        return res1.json;

        // const res = await ctx.db.qbAccount.findMany({
        //   where: {
        //     companyId: input.companyId,
        //   },
        // });

        // return res.map((d) => {
        //   const {
        //     accountId,
        //     companyId,
        //     createdAt,
        //     updatedAt,
        //     error,
        //     Id,
        //     MetaData,
        //     ParentRef,
        //     CurrencyRef,
        //     Active,
        //     sparse,
        //     SyncToken,
        //     AcctNum,
        //     CurrentBalanceWithSubAccounts,
        //     domain,
        //     SubAccount,
        //     FullyQualifiedName,
        //     ...rest
        //   } = d;
        //   // console.log(createdAt);

        //   return rest;
        // });
      } catch (e) {
        console.log("sad: --- ", e);
      }
    }),
});
