import bcrypt from "bcryptjs";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import OAuthClient from "intuit-oauth";

import {
  cbProcedure,
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "@/server/api/trpc";
import type { NextApiRequest } from "next";
import { env } from "@/env";
const oauthClient = new OAuthClient({
  clientId: env.QB_CLIENT_ID,
  clientSecret: env.QB_CLIENT_SECRET,
  environment: "production", // ‘sandbox’ or ‘production’
  redirectUri: "http://localhost:3000/api/trpc/user.cb",
});
const SALT_ROUNDS = 10;

export const userRouter = createTRPCRouter({
  // Get user's companies
  getUserCompanies: protectedProcedure.query(async ({ ctx }) => {
    const userCompanies = await ctx.db.userCompany.findMany({
      where: { userId: ctx.session.user.userId },
      include: { Company: true },
    });

    return userCompanies.map((uc) => ({
      userCompanyId: uc.userCompanyId,
      companyId: uc.companyId,
      companyName: (uc.Company as any).companyName,
      realmId: uc.Company.realmId,
      createdAt: uc.createdAt,
    }));
  }),

  auth: publicProcedure.mutation(({ input }) => {
    // AuthorizationUri
    const authUri = oauthClient.authorizeUri({
      scope: [
        OAuthClient.scopes.Accounting,
        OAuthClient.scopes.OpenId,
        OAuthClient.scopes.Email,
        OAuthClient.scopes.Phone,
      ],
    });
    return authUri;
  }),
  signUp: publicProcedure
    .input(
      z.object({
        name: z.string().min(1),
        email: z.string().min(1),
        password: z.string().min(1),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { name, password, email } = input;

      // Check if user already exists
      const existingUser = await ctx.db.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "User already exists",
        });
      }

      const salt = bcrypt.genSaltSync(SALT_ROUNDS);
      const hash = bcrypt.hashSync(password, salt);

      const user = await ctx.db.user.create({
        data: {
          email,
          name,
          password: hash,
        },
      });

      // Return user without password
      const { password: _, ...userWithoutPassword } = user;
      return userWithoutPassword;
    }),

  getUserInformation: protectedProcedure.query(async ({ ctx }) => {
    if (!ctx.session.user.userId) return null;
    return await ctx.db.userCompany.findMany({
      where: { userId: ctx.session.user.userId },
      include: { Company: true, UserCompanyState: true },
    });
  }),
  cb: publicProcedure.input(String).query(({ ctx, input }) => {
    oauthClient
      .createToken(ctx.url)
      .then(function (authResponse) {
        console.log("The Token is  " + JSON.stringify(authResponse.getJson()));
      })
      .catch(function (e) {
        console.error("The error message is :" + e);
        // console.error(e.intuit_tid);
      });

    // return ctx.headers;
  }),
});
