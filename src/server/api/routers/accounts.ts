import { z } from "zod";
import {
  createTRPCRouter,
  protectedProcedure,
  qbAuthMiddlewareV2,
  qbProcedure,
} from "@/server/api/trpc";

export const accountRouter = createTRPCRouter({
  getBankAccounts: protectedProcedure
    .input(z.object({ companyId: z.string() }))
    .query(async ({ ctx, input }) => {
      return await ctx.db.qbAccount.findMany({
        where: {
          companyId: input.companyId,
          AccountType: {
            in: ["Bank", "Credit Card"],
          },
        },
        select: {
          Id: true,
          Name: true,
        },
      });
    }),
});
