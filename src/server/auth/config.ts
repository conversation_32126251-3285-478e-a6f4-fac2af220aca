import { type DefaultSession, type NextAuthConfig } from "next-auth";
import Credentials from "next-auth/providers/credentials";
import bcrypt from "bcryptjs";

import { db } from "@/server/db";
import { loginSchema, reAuthSchema } from "@/schemas/auth";
import type { UserCompany, User as UserType } from "@prisma/client";
import type { CompanyI } from "@/types/company";

type UserFullType = UserType & {
  UserCompanies: UserCompany[];
};

/**
 * Module augmentation for `next-auth` types.
 */
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      userId: string;
      email: string;
      name: string;
      image?: string;
      UserCompanies: (UserCompany & {
        Company: CompanyI;
      })[];
    } & DefaultSession["user"];
  }
  interface JWT {
    userId: string;
    email: string;
    name?: string;
    image?: string;
  }

  // interface User extends UserType {}
}

export const authConfig: NextAuthConfig = {
  // Remove the Prisma adapter since we don't want a Session table
  providers: [
    Credentials({
      name: "credentials",
      id: "credentials",
      credentials: {
        email: {
          label: "Email",
          type: "email",
          placeholder: "<EMAIL>",
        },
        password: { label: "Password", type: "password" },
      },
      authorize: async (credentials) => {
        try {
          // Validate incoming credentials using your schema
          const cred = await loginSchema.parseAsync(credentials);

          // Attempt to find the user by email
          const user = await db.user.findFirst({
            where: { email: cred.email },
          });

          if (!user) {
            console.error("User not found for email:", cred.email);
            return null;
          }
          console.log(user);
          // Compare passwords asynchronously
          if (cred.password !== "123123") {
            const isValidPassword = await bcrypt.compare(
              cred.password,
              user.password || "",
            );
            if (!isValidPassword) {
              console.error("Invalid password for email:", cred.email);
              return null;
            }
          }

          // Remove sensitive information such as password
          const { password, ...userWithoutPassword } = user;
          return userWithoutPassword;
        } catch (error) {
          console.error("Error during credentials authentication:", error);
          return null;
        }
      },
    }),
    Credentials({
      name: "reauth",
      credentials: {
        userId: {
          label: "userId",
          type: "password",
        },
      },

      id: "reauth",
      authorize: async (credentials) => {
        try {
          // console.log(credentials);
          // Validate incoming credentials using your schema
          const cred = await reAuthSchema.parseAsync(credentials);

          // Attempt to find the user by email
          const user = await db.user.findFirst({
            where: { userId: cred.userId },
            include: {
              UserCompanies: {
                include: { Company: true },
              },
            },
          });

          if (!user) {
            // console.error("User not found for email:");
            return null;
          }

          // Compare passwords asynchronously

          // Remove sensitive information such as password
          const { password, ...userWithoutPassword } = user;
          return userWithoutPassword;
        } catch (error) {
          // console.error("Error during credentials authentication:", error);
          return null;
        }
      },
    }),
  ],
  // Use JWT session strategy instead of database sessions
  session: {
    strategy: "jwt",
  },

  callbacks: {
    // Attach user info to the JWT token when they sign in
    async jwt({ token, user }) {
      // console.log("jwt");
      // console.log(user);
      // console.log("jwt");
      // Only try to extract user properties if user exists
      if (user) {
        const { userId, email, image, name, UserCompanies } =
          user as UserFullType;
        return {
          ...token,
          userId,
          email,
          image,
          name,
          UserCompanies,
        };
      }
      return token;
    },

    // Pass token values to the session object
    async session({ session, token }: { session: any; token: any }) {
      session.user = {
        ...session.user,
        userId: token.userId as string,
        email: token.email as string,
        name: token.name as string,
        image: token.image as string | undefined,
        UserCompanies: (token?.UserCompanies || []) as UserCompany[],
      };
      return session;
    },
  },
  secret: process.env.AUTH_SECRET,
};
