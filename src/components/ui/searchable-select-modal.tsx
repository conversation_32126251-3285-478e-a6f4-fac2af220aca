"use client";

import * as React from "react";
import { Check, ChevronsUpDown } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";

interface SearchableSelectProps {
  options: { value: string; label: string }[];
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  className?: string;
}
export function SearchableSelectDialog({
  options,
  value,
  onValueChange,
  placeholder = "Select an option...",
  searchPlaceholder = "Search options...",
  emptyMessage = "No option found.",
  className,
}: SearchableSelectProps) {
  const [open, setOpen] = React.useState(false);
  //   const [value, setValue] = React.useState("");

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-[200px] justify-between break-words text-left whitespace-normal",
            className,
          )}
        >
            {value
              ? options.find((option) => option.value === value)?.label
              : placeholder}
          <ChevronsUpDown className="opacity-50" />
        </Button>
      </DialogTrigger>
      <DialogContent className="top-[10%] translate-y-0">
        <DialogHeader>
          <DialogTitle>{placeholder}</DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
        <Command className="rounded-lg border-none">
          <CommandInput placeholder={searchPlaceholder} className="h-12" />
          <CommandList className="max-h-[300px] overflow-y-auto">
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            <CommandGroup>
              {options.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.label}
                  onSelect={(currentValue) => {
                    onValueChange?.(
                      currentValue === value
                        ? ""
                        : options.find((o) => o.label === currentValue)
                            ?.value || currentValue,
                    );
                    setOpen(false);
                  }}
                  className="cursor-pointer"
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === option.value ? "opacity-100" : "opacity-0",
                    )}
                  />
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </DialogContent>
    </Dialog>

    // <Popover open={open} onOpenChange={setOpen}>
    //   <PopoverTrigger asChild>

    //   </PopoverTrigger>
    //   <PopoverContent className="w-[200px] p-0">
    //     <Command>
    //       <CommandInput placeholder={searchPlaceholder} className="h-9" />
    //       <CommandList>
    //         <CommandEmpty>{emptyMessage}</CommandEmpty>
    //         <CommandGroup>
    //           {options.map((option) => (
    //             <CommandItem
    //               key={option.value}
    //               value={option.label}
    //               onSelect={(currentValue) => {
    //                 onValueChange?.(
    //                   currentValue === value
    //                     ? ""
    //                     : options.find((o) => o.label === currentValue)
    //                         ?.value || currentValue,
    //                 );
    //                 setOpen(false);
    //               }}
    //             >
    //               <Check
    //                 className={cn(
    //                   "mr-2 h-4 w-4",
    //                   value === option.value ? "opacity-100" : "opacity-0",
    //                 )}
    //               />
    //               {option.label}
    //             </CommandItem>
    //           ))}
    //         </CommandGroup>
    //       </CommandList>
    //     </Command>
    //   </PopoverContent>
    // </Popover>
  );
}
