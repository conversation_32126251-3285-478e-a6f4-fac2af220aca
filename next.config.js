/**
 * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially useful
 * for Docker builds.
 */
import "./src/env.js";

/** @type {import("next").NextConfig} */
const config = {
  // output: "standalone",
  // experimental: {
  //   // Increase the body size limit for API routes to handle large images
  //   serverComponentsExternalPackages: [],
  // },
  // Configure body size limits
  // api: {
  //   bodyParser: {
  //     sizeLimit: "50mb", // Increase limit to 50MB to handle large base64 images
  //   },
  // },
  webpack: (config, { isServer }) => {
    // Fix for node-pre-gyp issue
    config.module.rules.push({
      test: /\.html$/,
      use: "ignore-loader",
    });

    return config;
  },
};

export default config;
